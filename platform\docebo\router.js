const express = require("express");
const router = express.Router();

const {
    userManagement,
    sessionManagement,
    lpManagement,
    courseEnrollmentCreated,
    courseUnEnrollment,
    courseCompleted,
    getUserEnrollments
} = require("./controller");

router.post("/user/manage", userManagement);
router.post("/session/manage", sessionManagement);
router.post("/lp/manage", lpManagement);
router.post("/user/enrollment/created", courseEnrollmentCreated);
router.post("/user/enrollment/deleted", courseUnEnrollment);
router.post("/user/enrollment/completed", courseCompleted);
router.get("/user/:userId/enrollments", getUserEnrollments);

module.exports = router;
