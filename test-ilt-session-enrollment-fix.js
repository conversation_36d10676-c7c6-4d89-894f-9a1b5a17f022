require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createIltSessionEnrollment } = require('./platform/salesforce/services');

async function testIltSessionEnrollmentFix() {
    try {
        console.log('🧪 Testing ILT Session Enrollment Fix');
        console.log('=' .repeat(50));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Get sample course and user for testing
        console.log('\n🔍 Finding sample course and user for testing...');
        console.log('-'.repeat(50));
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});
        
        if (!sampleCourse || !sampleUser) {
            console.error("❌ No sample course or user found for testing");
            return;
        }
        
        console.log(`✅ Sample Course: ${sampleCourse.Course_Name__c}`);
        console.log(`   Course External ID: ${sampleCourse.Course_External_Id__c}`);
        console.log(`   Salesforce ID: ${sampleCourse.Id}`);
        console.log(`✅ Sample User: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c}`);
        console.log(`   User Unique ID: ${sampleUser.User_Unique_Id__c}`);
        console.log(`   Salesforce ID: ${sampleUser.Id}`);

        // Step 2: Test ILT session enrollment creation
        console.log('\n🧪 Testing ILT session enrollment creation...');
        console.log('-'.repeat(50));
        
        const testEnrollmentData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            completion: 0,
            completion_date: null,
            score: 0
        };
        
        console.log(`📋 Test enrollment data:`);
        console.log(`   Course ID (Docebo): ${testEnrollmentData.course_id}`);
        console.log(`   User ID (Docebo): ${testEnrollmentData.user_id}`);
        console.log(`   Expected Enrollment_ID__c: UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`);
        console.log(`   Expected Course__c (Salesforce): ${sampleCourse.Id}`);
        console.log(`   Expected Docebo_User__c (Salesforce): ${sampleUser.Id}`);
        
        // Clean up any existing test enrollment
        const testEnrollmentId = `UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`;
        try {
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (existingEnrollment) {
                await conn.sobject("Docebo_CourseEnrollment__c").delete(existingEnrollment.Id);
                console.log(`🗑️ Deleted existing test enrollment: ${existingEnrollment.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing enrollment to clean up');
        }
        
        // Create the enrollment using the fixed function
        console.log('\n🔧 Creating ILT session enrollment...');
        const result = await createIltSessionEnrollment(testEnrollmentData);
        
        if (result) {
            console.log('✅ ILT session enrollment creation successful!');
            
            // Verify the created enrollment
            const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (createdEnrollment) {
                console.log(`\n✅ Enrollment verified: ${createdEnrollment.Id}`);
                console.log(`   Enrollment_ID__c: ${createdEnrollment.Enrollment_ID__c}`);
                console.log(`   Course__c: ${createdEnrollment.Course__c || 'MISSING ❌'}`);
                console.log(`   Docebo_User__c: ${createdEnrollment.Docebo_User__c || 'MISSING ❌'}`);
                console.log(`   Completion__c: ${createdEnrollment.Completion__c}`);
                console.log(`   Score__c: ${createdEnrollment.Score__c}`);
                
                // Verify the associations are correct
                const correctCourseAssociation = createdEnrollment.Course__c === sampleCourse.Id;
                const correctUserAssociation = createdEnrollment.Docebo_User__c === sampleUser.Id;
                
                console.log(`\n🔍 Association Verification:`);
                console.log(`   Course Association: ${correctCourseAssociation ? '✅ CORRECT' : '❌ WRONG'}`);
                console.log(`     Expected: ${sampleCourse.Id}`);
                console.log(`     Actual: ${createdEnrollment.Course__c}`);
                console.log(`   User Association: ${correctUserAssociation ? '✅ CORRECT' : '❌ WRONG'}`);
                console.log(`     Expected: ${sampleUser.Id}`);
                console.log(`     Actual: ${createdEnrollment.Docebo_User__c}`);
                
                if (correctCourseAssociation && correctUserAssociation) {
                    console.log('\n🎉 SUCCESS! ILT session enrollment fix is working correctly!');
                    console.log('✅ Course association: Using Salesforce Course ID');
                    console.log('✅ User association: Using Salesforce User ID');
                    console.log('✅ Enrollment ID format: Correct UE-courseId-userId format');
                } else {
                    console.log('\n❌ ISSUE: Associations are not correct');
                }
            } else {
                console.log('❌ Created enrollment not found');
            }
        } else {
            console.log('❌ ILT session enrollment creation failed');
        }

        // Step 3: Test error handling
        console.log('\n🧪 Testing error handling...');
        console.log('-'.repeat(50));
        
        // Test with non-existent course
        const invalidCourseData = {
            course_id: 999999, // Non-existent course ID
            user_id: sampleUser.User_Unique_Id__c,
            completion: 0,
            score: 0
        };
        
        console.log('📋 Testing with non-existent course ID: 999999');
        const invalidCourseResult = await createIltSessionEnrollment(invalidCourseData);
        console.log(`   Result: ${invalidCourseResult ? 'Unexpected Success' : '✅ Correctly Failed'}`);
        
        // Test with non-existent user
        const invalidUserData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: 999999, // Non-existent user ID
            completion: 0,
            score: 0
        };
        
        console.log('📋 Testing with non-existent user ID: 999999');
        const invalidUserResult = await createIltSessionEnrollment(invalidUserData);
        console.log(`   Result: ${invalidUserResult ? 'Unexpected Success' : '✅ Correctly Failed'}`);

        // Step 4: Summary
        console.log('\n📊 SUMMARY');
        console.log('=' .repeat(50));
        console.log('🔧 FIXES IMPLEMENTED:');
        console.log('✅ Course__c now uses Salesforce Course ID instead of Docebo course_id');
        console.log('✅ Docebo_User__c now uses Salesforce User ID instead of Docebo user_id');
        console.log('✅ Added proper lookup validation for both course and user');
        console.log('✅ Added error handling for missing courses/users');
        console.log('✅ Maintains correct Enrollment_ID__c format: UE-courseId-userId');
        
        console.log('\n💡 IMPACT:');
        console.log('✅ ILT session enrollments will now have proper associations');
        console.log('✅ Course and user lookups will work correctly in Salesforce');
        console.log('✅ Prevents creation of enrollments with invalid references');

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
console.log('🔄 Starting ILT session enrollment fix test...');
testIltSessionEnrollmentFix()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
