// Map Docebo course data to Salesforce Course_Enrollment__c fields
function mapDoceboCourseToSalesforce(doceboCourseData, enrollmentData = {}) {
    try {
        const courseData = doceboCourseData || {};
        
        // Helper function to safely extract nested values
        const safeGet = (obj, path, defaultValue = "") => {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : defaultValue;
            }, obj);
        };
        
        // Helper function to format date
        const formatDate = (dateString) => {
            if (!dateString) return "";
            try {
                return new Date(dateString).toISOString().split('T')[0];
            } catch (error) {
                return "";
            }
        };
        
        // Helper function to format skills array
        const formatSkills = (skillsArray) => {
            if (!Array.isArray(skillsArray)) return "";
            return skillsArray.map(skill => skill.name || skill).join(", ");
        };
        
        const mappedData = {
            // Core Course Identification
            Course_Unique_Id__c: String(courseData.id || ""),
            Course_External_Id__c: courseData.uid || "",
            Course_Internal_ID__c: courseData.id || 0,
            Course_Code__c: courseData.code || "",
            Course_Status__c: courseData.status || "",
            
            // Course Information
            Course_Name__c: courseData.name || "",
            Name: courseData.name || "Unknown Course",
            Description__c: courseData.description || courseData.short_description || "",
            Course_Type__c: courseData.type || "",
            Type__c: courseData.type || "",
            Skills_in_course__c: formatSkills(courseData.skills),
            Language__c: safeGet(courseData, 'language.name', ''),
            
            // Course Metadata
            Course_Creation_Date__c: formatDate(courseData.created_on),
            Course_Duration__c: safeGet(courseData, 'time_options.duration.days', 0),
            Course_Start_Date__c: formatDate(safeGet(courseData, 'time_options.date_begin')),
            Course_End_Date__c: formatDate(safeGet(courseData, 'time_options.date_end')),
            Last_Update_Date__c: formatDate(courseData.updated_on),
            
            // Course Category
            Course_Category__c: safeGet(courseData, 'category.name', ''),
            Course_Category_Code__c: safeGet(courseData, 'category.code', ''),
            
            // Course Content
            Slug__c: courseData.slug_name || "",
            Thumbnail__c: safeGet(courseData, 'thumbnail.url', ''),
            Credits_CEUs__c: courseData.credits || 0,
            
            // Course Settings
            Effective__c: courseData.status === 'published',
            Deleted__c: courseData.status === 'deleted' || courseData.status === 'suspended',
            Deletion_Date__c: courseData.status === 'deleted' ? formatDate(courseData.updated_on) : "",
            Course_has_expired__c: courseData.status === 'expired' ? "Yes" : "No",
            
            // Enrollment & Progress (from enrollment data if available)
            Enrollment_Date__c: formatDate(enrollmentData.enrollment_date || enrollmentData.date_inscr),
            Course_Progress__c: enrollmentData.completion_percentage || enrollmentData.progress || 0,
            Score__c: enrollmentData.score || enrollmentData.final_score || 0,
            Session_Time_min__c: Math.round((enrollmentData.total_time || 0) / 60), // Convert seconds to minutes
            
            // System Fields
            CreatedById: safeGet(courseData, 'created_by.id', ''),
            LastModifiedById: "",
            Owner__c: safeGet(courseData, 'created_by.fullname', ''),
            OwnerId: "005O400000BxnnxIAB", // Default owner
            
            // Additional Fields
            Course_Link__c: courseData.deeplink_url || "",
            Number_of_actions__c: 0, // Not available in course API
            Number_of_sessions__c: 0, // Not available in course API
            User_Course_Level__c: enrollmentData.level || "",
            Reset_Password_Link__c: "",
            Training_Material_from_Mobile_App__c: 0,
            Time_in_Training_Material_from_Mobile_Ap__c: 0,
            Training_Material_Access_from_Mobile_App__c: 0,
            Training_Material_Time_sec__c: enrollmentData.total_time || 0
        };
        
        console.log(`📋 Mapped course data for: ${mappedData.Course_Name__c} (ID: ${mappedData.Course_Unique_Id__c})`);
        
        return mappedData;
        
    } catch (error) {
        console.error('💥 Error mapping course data:', error);
        return null;
    }
}

// Enhanced function to get comprehensive course data from Docebo
async function getComprehensiveCourseData(courseId, userId = null) {
    try {
        const doceboService = require('../../docebo/services');
        
        console.log(`📥 Fetching comprehensive course data for Course ID: ${courseId}`);
        
        // Get basic course info
        const courseResult = await doceboService.getCourseInfo(courseId);
        
        if (!courseResult || courseResult.status !== 200) {
            console.error(`❌ Failed to get course info for ID: ${courseId}`);
            return null;
        }
        
        const courseData = courseResult.data;
        let enrollmentData = {};
        
        // If userId is provided, get enrollment-specific data
        if (userId) {
            try {
                // You can add enrollment-specific API calls here if available
                console.log(`📋 Getting enrollment data for User ${userId} in Course ${courseId}`);
                // enrollmentData = await doceboService.getUserCourseEnrollment(userId, courseId);
            } catch (enrollmentError) {
                console.log(`⚠️ Could not get enrollment data: ${enrollmentError.message}`);
            }
        }
        
        // Map the data to Salesforce format
        const mappedData = mapDoceboCourseToSalesforce(courseData, enrollmentData);
        
        return {
            success: true,
            courseData: courseData,
            enrollmentData: enrollmentData,
            mappedData: mappedData
        };
        
    } catch (error) {
        console.error('💥 Error getting comprehensive course data:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

module.exports = {
    mapDoceboCourseToSalesforce,
    getComprehensiveCourseData
};
