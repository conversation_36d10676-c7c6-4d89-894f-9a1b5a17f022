const getConnection = require("../common/getConnection");

async function deleteIltSessionEnrollment(enrollmentData) {
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection in deleteIltSessionEnrollment");
            return false;
        }
        
        // Build the enrollment ID in the same format used for creation
        const enrollmentId = `UE-${enrollmentData.course_id}-${enrollmentData.user_id}`;
        
        // Find the enrollment record
        const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ Enrollment_ID__c: enrollmentId });
            
        if (!existingEnrollment) {
            console.error(`Enrollment not found for deletion: ${enrollmentId}`);
            return false;
        }
        
        // Delete the enrollment record
        const deleteResult = await conn.sobject("Docebo_CourseEnrollment__c")
            .destroy(existingEnrollment.Id);
            
        if (deleteResult.success) {
            console.log(`Successfully deleted enrollment: ${enrollmentId}`);
            return true;
        } else {
            console.error(`Failed to delete enrollment: ${enrollmentId}`, deleteResult.errors);
            return false;
        }
    } catch (err) {
        console.error("Error in deleteIltSessionEnrollment:", err);
        return false;
    }
}

module.exports = {
    deleteIltSessionEnrollment
};