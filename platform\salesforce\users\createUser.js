const getConnection = require("../common/getConnection");
const getSalesForceUserId = require("../common/getSalesForceUserId");

// Helper functions to map Docebo values to valid Salesforce picklist values
function mapRoleTypeToValidValue(doceboRoleType) {
    if (!doceboRoleType) return "";

    const roleMapping = {
        "Management": "Operations/Business Management",
        "Technical": "Programs",
        "Administrative": "Administrative",
        "Executive": "Executive Director",
        "Communications": "Communications",
        "Data": "Data and Research",
        "Policy": "Policy/Government",
        "Community": "Community Engagement/Organizing",
        "Fundraising": "Fundraising/Development",
        "Board": "Board of Directors"
    };

    return roleMapping[doceboRoleType] || "Other";
}

function mapRaceToValidValue(doceboRace) {
    if (!doceboRace) return "";

    const raceMapping = {
        "Asian": "Asian",
        "Black": "Black or African American",
        "African American": "Black or African American",
        "White": "White",
        "Hispanic": "Hispanic or Latine",
        "Latino": "Hispanic or Latine",
        "Latina": "Hispanic or Latine",
        "Latine": "Hispanic or Latine",
        "Native American": "American Indian or Alaskan Native",
        "Pacific Islander": "Native Hawaiian or Other Pacific Islander",
        "Multi-Racial": "Multi-Racial",
        "Mixed": "Multi-Racial",
        "Prefer not to say": "Prefer not to respond",
        "Prefer not to respond": "Prefer not to respond"
    };

    return raceMapping[doceboRace] || "Other";
}

function mapGenderToValidValue(doceboGender) {
    if (!doceboGender) return "";

    const genderMapping = {
        "Male": "Man",
        "Female": "Woman",
        "Man": "Man",
        "Woman": "Woman",
        "Non-binary": "Non-Binary or other gender identity",
        "Non-Binary": "Non-Binary or other gender identity",
        "Prefer not to say": "Prefer not to respond",
        "Prefer Not To Say": "Prefer not to respond"
    };

    return genderMapping[doceboGender] || "Prefer not to respond";
}

// Helper function to update existing Contact or Lead when Docebo_Users__c already exists
async function updateExistingContactOrLead(conn, tmpUserInfo, userInfo) {
    try {
        console.log(`🔍 Checking for existing Contact or Lead with email: ${tmpUserInfo.Email__c}`);

        // First check for existing Contact
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: tmpUserInfo.Email__c })
            .execute();

        if (existingContacts.length > 0) {
            // Update existing Contact
            const contactId = existingContacts[0].Id;
            console.log(`📝 Updating existing Contact: ${contactId}`);

            const contactUpdateData = {
                Id: contactId,
                LastName: tmpUserInfo.Last_Name__c,
                FirstName: tmpUserInfo.First_Name__c,
                Email: tmpUserInfo.Email__c,
                Title: tmpUserInfo.Job_Title__c || "",

                // FIX: Add comprehensive fields for Contact update with correct field names
                Created_by_Docebo_API__c: true,
                GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                // Additional fields with correct names
                Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
                Phone: userInfo.user_data.phone || "",
                Languages__c: userInfo.user_data.language || "",
                mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",

                // FIX: Add missing required fields
                Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                Company__c: tmpUserInfo.Organization_Name__c || "",
                Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                Initiative__c: tmpUserInfo.Initiative__c || "",
                LeadSource: "Docebo Platform",
                NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                Rating__c: tmpUserInfo.Rating__c || "Warm",
                Time_Zone__c: userInfo.user_data.timezone || "",
                Type__c: "Backbone Staff",
                Website__c: tmpUserInfo.Organization_URL__c || "",
                Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                FTE__c: "Full-Time",
                Gateway__c: "Docebo API",
                Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
                No_Longer_Leadership__c: false,
                No_Longer_Staff__c: false,
                Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
                Contact_Type__c: "Other"
            };

            const contactUpdateResult = await conn.sobject("Contact").update(contactUpdateData);
            if (contactUpdateResult.success) {
                console.log(`✅ Contact updated successfully: ${contactId}`);
            } else {
                console.error("❌ Contact update failed:", contactUpdateResult.errors);
            }
            return;
        }

        // If no Contact found, check for existing Lead
        const existingLeads = await conn.sobject("Lead")
            .find({ Email: tmpUserInfo.Email__c })
            .execute();

        if (existingLeads.length > 0) {
            // Update existing Lead
            const leadId = existingLeads[0].Id;
            console.log(`📝 Updating existing Lead: ${leadId}`);

            const leadUpdateData = {
                Id: leadId,
                LastName: tmpUserInfo.Last_Name__c,
                FirstName: tmpUserInfo.First_Name__c,
                Email: tmpUserInfo.Email__c,
                Company: tmpUserInfo.Organization_Name__c || "-",
                Title: tmpUserInfo.Job_Title__c || "",
                Website: tmpUserInfo.Organization_URL__c || "",

                // Custom fields with proper mapping
                GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                // Additional fields with improved mapping
                Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
                Salutation: getAdditionalData(userInfo.additional_fields || [], "27") || "",
                Phone: userInfo.user_data.phone || "",
                Languages__c: userInfo.user_data.language || "",
                mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "", // FIX: Correct field name

                // FIX: Add missing required fields for Lead update
                accountid__c: tmpUserInfo.Account_ID__c || "",
                AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
                Industry: tmpUserInfo.Industry__c || "Not For Profit",
                Initiative__c: tmpUserInfo.Initiative__c || "",
                NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
                Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
                Rating: tmpUserInfo.Rating__c || "Warm",
                Time_Zone__c: userInfo.user_data.timezone || "",
                Type__c: "Backbone Staff",
                Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",
                Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
            };

            const leadUpdateResult = await conn.sobject("Lead").update(leadUpdateData);
            if (leadUpdateResult.success) {
                console.log(`✅ Lead updated successfully: ${leadId}`);
            } else {
                console.error("❌ Lead update failed:", leadUpdateResult.errors);
            }
            return;
        }

        console.log(`ℹ️ No existing Contact or Lead found for email: ${tmpUserInfo.Email__c}`);

    } catch (err) {
        console.error("❌ Error in updateExistingContactOrLead:", err);
    }
}

async function createNewUser(userInfo, userListedInfo) {
    // First check if user already exists by ID
    let userExistRes = await getSalesForceUserId(userInfo.user_data.user_id);
    
    // Prepare user data
    let tmpUserInfo = tidyData(userInfo, userListedInfo);
    
    // Ensure LastName is set for Salesforce Lead creation
    if (!tmpUserInfo.Last_Name__c || tmpUserInfo.Last_Name__c.trim() === "") {
        tmpUserInfo.Last_Name__c = "Unknown";
    }
    
    console.log("====== get connection for create/update User ======");
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in createNewUser");
        return false;
    }
    
    // If user already exists, update Docebo_Users__c AND check for Lead/Contact to update
    if (userExistRes !== null) {
        console.log("User already exists in Salesforce, updating: ", userInfo.user_data.user_id);

        // Set the ID for update
        tmpUserInfo.Id = userExistRes;

        try {
            // Update Docebo_Users__c record
            const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
            if (updateResult.success) {
                console.log("Docebo_Users__c updated successfully:", userInfo.user_data.user_id);

                // Now check if there's a Contact or Lead to update by email
                await updateExistingContactOrLead(conn, tmpUserInfo, userInfo);

                return true;
            } else {
                console.error("Docebo_Users__c update failed:", updateResult.errors);
                return false;
            }
        } catch (err) {
            console.error("Error updating Docebo_Users__c:", err);
            return false;
        }
    }
    
    // If we get here, user doesn't exist by ID, but let's double-check by User_Unique_Id__c
    try {
        // First check if a user with this User_Unique_Id__c already exists
        const uniqueId = parseInt(userInfo.user_data.user_id, 10);
        if (!isNaN(uniqueId)) {
            console.log(`Double-checking if user with User_Unique_Id__c=${uniqueId} exists`);
            const existingUser = await conn.sobject("Docebo_Users__c")
                .findOne({ User_Unique_Id__c: uniqueId });
                
            if (existingUser) {
                console.log(`Found user by User_Unique_Id__c: ${existingUser.Id}`);
                // Update the existing user
                tmpUserInfo.Id = existingUser.Id;
                const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
                if (updateResult.success) {
                    console.log(`Docebo_Users__c updated successfully by User_Unique_Id__c: ${uniqueId}`);

                    // Also check for Contact or Lead to update
                    await updateExistingContactOrLead(conn, tmpUserInfo, userInfo);

                    return true;
                } else {
                    console.error(`Failed to update user by User_Unique_Id__c: ${uniqueId}`, updateResult.errors);
                    return false;
                }
            }
        }
        
        // If we get here, user really doesn't exist, so create a new one
        if (conn.accessToken) {
            const existingContacts = await conn.sobject("Contact")
                .find({ Email: tmpUserInfo.Email__c })
                .execute();
            if (existingContacts.length > 0) {
                const contactId = existingContacts[0].Id;
                const contactUpdateData = {
                    Id: contactId,
                    LastName: tmpUserInfo.Last_Name__c,
                    FirstName: tmpUserInfo.First_Name__c,
                    Email: tmpUserInfo.Email__c,
                    Title: tmpUserInfo.Job_Title__c || "",

                    // FIX: Add comprehensive Contact field mappings
                    Created_by_Docebo_API__c: true,
                    GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                    Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                    Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                    Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                    // Additional fields with correct names
                    Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                    Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "",
                    Phone: userInfo.user_data.phone || "",
                    Languages__c: userInfo.user_data.language || "",
                    mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                    mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                    mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                    mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                    mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                    Position_Role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "",

                    // FIX: Add missing required fields
                    Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                    Company__c: tmpUserInfo.Organization_Name__c || "",
                    Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                    Initiative__c: tmpUserInfo.Initiative__c || "",
                    LeadSource: "Docebo Platform",
                    NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                    Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                    Rating__c: tmpUserInfo.Rating__c || "Warm",
                    Time_Zone__c: userInfo.user_data.timezone || "",
                    Type__c: "Backbone Staff",
                    Website__c: tmpUserInfo.Organization_URL__c || "",
                    Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                    Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                    FTE__c: "Full-Time",
                    Gateway__c: "Docebo API",
                    Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                    Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
                    No_Longer_Leadership__c: false,
                    No_Longer_Staff__c: false,
                    Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
                    Contact_Type__c: "Other"
                };
                const updateResult = await conn.sobject("Contact").update(contactUpdateData);
                if (updateResult.success) {
                    tmpUserInfo.Contact__c = contactId;
                }
            } else {
                const accountData = {
                    Name: tmpUserInfo.Full_Name__c + tmpUserInfo.User_Unique_Id__c,
                    Website: "",
                };
                const accountResult = await conn.sobject("Account").create(accountData);
                if (accountResult.success) {
                    // tmpUserInfo.Account__c = accountResult.id; // Field doesn't exist on Docebo_Users__c
                    // Create lead data with ALL the same fields that contacts have
                    // These fields should be created on the Lead object to match Contact capabilities
                    const leadData = {
                        // Standard Lead fields mapped according to your specifications
                        LastName: tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "" ? tmpUserInfo.Last_Name__c : "Unknown",
                        FirstName: tmpUserInfo.First_Name__c,
                        Email: tmpUserInfo.Email__c,
                        Company: tmpUserInfo.Organization_Name__c || "-",
                        Title: tmpUserInfo.Job_Title__c || "",
                        Website: tmpUserInfo.Organization_URL__c || "",
                        Status: "Open - Not Contacted",

                        // Custom fields mapped according to your field mapping list
                        Created_by_Docebo_API__c: true,
                        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                        // Additional fields from your mapping - now with proper data
                        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                        Fax: getAdditionalData(userInfo.additional_fields || [], "26") || "", // Map from additional field if available
                        Salutation: getAdditionalData(userInfo.additional_fields || [], "27") || "", // Map from additional field if available
                        Phone: userInfo.user_data.phone || "", // FIX: Use actual phone from user_data
                        Languages__c: userInfo.user_data.language || "", // FIX: Use actual language from user_data
                        mailingcity__c: getAdditionalData(userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                        mailingcountry__c: getAdditionalData(userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                        mailingpostalcode__c: getAdditionalData(userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                        mailingstate__c: getStateLabel(userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                        mailingstreet__c: getAdditionalData(userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                        position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "", // FIX: Correct field name
                        LeadSource: "Docebo Platform",

                        // FIX: Add missing required fields
                        accountid__c: tmpUserInfo.Account_ID__c || "",
                        AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
                        Industry: tmpUserInfo.Industry__c || "Not For Profit", // Default to common value
                        Initiative__c: tmpUserInfo.Initiative__c || "",
                        NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
                        Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
                        Rating: tmpUserInfo.Rating__c || "Warm", // Default rating
                        Time_Zone__c: userInfo.user_data.timezone || "",
                        Type__c: "Backbone Staff", // Default type
                        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",
                        // REMOVED: These fields don't exist on Lead object
                        // Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                        // Industry__c: tmpUserInfo.Industry__c || "",
                        // NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                        // Rating__c: tmpUserInfo.Rating__c || "",
                        // TimeZone: userInfo.user_data.timezone || "",
                        // Account and system fields (accountid field doesn't exist on Lead)
                        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                        FTE__c: "Full-Time", // Default value
                        Gateway__c: "Docebo API",
                        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                        Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                        No_Longer_Leadership__c: false, // Default value
                        No_Longer_Staff__c: false, // Default value
                        Number_of_Years_in_the_Partnership__c: 0, // Not available in Docebo data
                        OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB",
                        Contact_Type__c: "Other"
                    };

                    // Enhanced logging for Lead creation
                    console.log(`🎯 CREATING LEAD for User ${tmpUserInfo.User_Unique_Id__c}:`);
                    console.log("Lead data being created:", JSON.stringify(leadData, null, 2));

                    // Log key Lead fields for verification
                    console.log("📊 KEY LEAD FIELDS:");
                    console.log(`   Company: "${leadData.Company}"`);
                    console.log(`   Title: "${leadData.Title}"`);
                    console.log(`   Languages__c: "${leadData.Languages__c}"`);
                    console.log(`   mailingcity__c: "${leadData.mailingcity__c}"`); // FIX: Use correct field name
                    console.log(`   mailingstate__c: "${leadData.mailingstate__c}"`); // FIX: Use correct field name
                    console.log(`   GenderIdentity: "${leadData.GenderIdentity}"`); // FIX: Use correct field name
                    console.log(`   Race__c: "${leadData.Race__c}"`);
                    console.log(`   Role_Type__c: "${leadData.Role_Type__c}"`);

                    try {
                        const leadResult = await conn.sobject("Lead").create(leadData);
                        if (leadResult.success) {
                            console.log(`Lead created successfully: ${leadResult.id}`);

                            // Store the lead ID for later association (don't set it in tmpUserInfo yet)
                            var createdLeadId = leadResult.id;
                            console.log(`✅ Lead created, will associate after Docebo_Users__c creation: ${createdLeadId}`);
                        } else {
                            console.error("Lead creation failed:", leadResult.errors);
                        }
                    } catch (leadError) {
                        console.error("Error creating lead:", leadError);
                    }
                }
            }
            console.log("====== Create Object ======");
            
            try {
                const result = await conn.sobject("Docebo_Users__c").create(tmpUserInfo);
                if (result.success) {
                    console.log(`User created successfully: ${userInfo.user_data.user_id}`);

                    // Now associate with Lead if one was created
                    if (typeof createdLeadId !== 'undefined' && createdLeadId) {
                        try {
                            // FIX: First verify the Lead exists and is accessible
                            const leadExists = await conn.sobject("Lead").findOne({ Id: createdLeadId });
                            if (leadExists) {
                                const linkResult = await conn.sobject("Docebo_Users__c").update({
                                    Id: result.id,
                                    Lead__c: createdLeadId
                                });

                                if (linkResult.success) {
                                    console.log(`✅ Docebo_Users__c ${result.id} linked to Lead ${createdLeadId}`);
                                } else {
                                    console.error("❌ Failed to link Docebo_Users__c to Lead:", linkResult.errors);
                                    // Try to find an alternative Lead by email if the direct link fails
                                    await tryAlternativeLeadAssociation(conn, result.id, tmpUserInfo.Email__c);
                                }
                            } else {
                                console.log(`⚠️ Lead ${createdLeadId} not found or not accessible, trying email lookup`);
                                await tryAlternativeLeadAssociation(conn, result.id, tmpUserInfo.Email__c);
                            }
                        } catch (linkError) {
                            console.error("❌ Error linking Docebo_Users__c to Lead:", linkError);
                            // Try alternative association method
                            await tryAlternativeLeadAssociation(conn, result.id, tmpUserInfo.Email__c);
                        }
                    }

                    return true;
                } else {
                    console.error(`User creation failed: ${userInfo.user_data.user_id}`, result.errors);
                    return false;
                }
            } catch (err) {
                // If creation fails due to duplicate, try to find and update the user
                if (err.errorCode === 'DUPLICATE_VALUE' && err.message.includes('User_Unique_Id__c duplicates value')) {
                    console.error(`Duplicate user detected during creation, attempting to update: ${userInfo.user_data.user_id}`);

                    // Extract the record ID from the error message
                    const match = err.message.match(/id: ([a-zA-Z0-9]+)/);
                    if (match && match[1]) {
                        const existingId = match[1];
                        console.log(`Found existing user ID from error: ${existingId}`);

                        // Update the existing record
                        tmpUserInfo.Id = existingId;
                        const updateResult = await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
                        if (updateResult.success) {
                            console.log(`Docebo_Users__c updated after duplicate detection: ${userInfo.user_data.user_id}`);

                            // Also check for Contact or Lead to update
                            await updateExistingContactOrLead(conn, tmpUserInfo, userInfo);

                            // FIX: Try to associate with Lead if one was created and exists
                            if (typeof createdLeadId !== 'undefined' && createdLeadId) {
                                try {
                                    // First verify the Lead still exists
                                    const leadExists = await conn.sobject("Lead").findOne({ Id: createdLeadId });
                                    if (leadExists) {
                                        const linkResult = await conn.sobject("Docebo_Users__c").update({
                                            Id: existingId,
                                            Lead__c: createdLeadId
                                        });

                                        if (linkResult.success) {
                                            console.log(`✅ Duplicate user ${existingId} linked to Lead ${createdLeadId}`);
                                        } else {
                                            console.log(`⚠️ Could not link duplicate user to Lead:`, linkResult.errors);
                                        }
                                    } else {
                                        console.log(`⚠️ Lead ${createdLeadId} no longer exists, skipping association`);
                                    }
                                } catch (linkError) {
                                    console.log(`⚠️ Could not link duplicate user to Lead: ${linkError.message}`);
                                }
                            }

                            return true;
                        }
                    }
                }
                console.error(`Error creating user: ${userInfo.user_data.user_id}`, err);
                return false;
            }
        }
        return false;
    } catch (err) {
        console.error(`Unexpected error in createNewUser: ${err.message}`);
        return false;
    }
}

async function getTotalUserList() {
    const conn = await getConnection();
    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return [];
    }

    try {
        let allUsers = [];
        let result = await conn.query(
            "SELECT User_Unique_Id__c FROM Docebo_Users__c WHERE User_Unique_Id__c != null"
        );

        allUsers.push(...result.records);

        while (!result.done) {
            result = await conn.queryMore(result.nextRecordsUrl);
            allUsers.push(...result.records);
        }

        return allUsers.map(user => user.User_Unique_Id__c);
    } catch (err) {
        console.error("Error fetching all Salesforce users:", err);
        return [];
    }
}

async function saveUsersInBatch(users) {
    const batchSize = 200;
    const conn = await getConnection();

    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return false;
    }

    for (let i = 0; i < users.length; i += batchSize) {
        const batch = users.slice(i, i + batchSize);
        console.log(`Processing batch ${i / batchSize + 1}: ${batch.length} users`);

        const batchData = [];

        for (const user of batch) {
            try {
                const tmpUserInfo = tidyData(user.userInfo, user.userListedInfo);
                console.log("This is user info: ", user.userInfo);
                console.log("This is user listed info: ", user.userListedInfo);

                const existingContacts = await conn.sobject("Contact")
                    .find({ Email: tmpUserInfo.Email__c })
                    .execute();

                if (existingContacts.length > 0) {
                    const contactId = existingContacts[0].Id;
                    const contactUpdateData = {
                        Id: contactId,
                        LastName: tmpUserInfo.Last_Name__c,
                        FirstName: tmpUserInfo.First_Name__c,
                        Email: tmpUserInfo.Email__c,
                        Title: tmpUserInfo.Job_Title__c || "",

                        // FIX: Add comprehensive Contact field mappings (same as above)
                        Created_by_Docebo_API__c: true,
                        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

                        // Additional fields with correct names
                        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                        Fax: "",
                        Phone: "",
                        Languages__c: "",
                        mailingcity__c: "", // FIX: Correct field name
                        mailingcountry__c: "", // FIX: Correct field name
                        mailingpostalcode__c: "", // FIX: Correct field name
                        mailingstate__c: "", // FIX: Correct field name
                        mailingstreet__c: "", // FIX: Correct field name
                        Position_Role__c: tmpUserInfo.Role_Type__c || "",

                        // FIX: Add missing required fields
                        Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
                        Company__c: tmpUserInfo.Organization_Name__c || "",
                        Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
                        Initiative__c: tmpUserInfo.Initiative__c || "",
                        LeadSource: "Docebo Platform",
                        NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
                        Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
                        Rating__c: tmpUserInfo.Rating__c || "Warm",
                        Time_Zone__c: tmpUserInfo.Time_Zone__c || "",
                        Type__c: "Backbone Staff",
                        Website__c: tmpUserInfo.Organization_URL__c || "",
                        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

                        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                        FTE__c: "Full-Time",
                        Gateway__c: "Docebo API",
                        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                        Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
                        No_Longer_Leadership__c: false,
                        No_Longer_Staff__c: false,
                        Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
                        Contact_Type__c: "Other"
                    };
                    const updateResult = await conn.sobject("Contact").update(contactUpdateData);
                    if (updateResult.success) {
                        tmpUserInfo.Contact__c = contactId;
                    }
                } else {
                    const accountData = {
                        Name: tmpUserInfo.Full_Name__c + tmpUserInfo.User_Unique_Id__c,
                        Website: "",
                    };
                    const accountResult = await conn.sobject("Account").create(accountData);
                    if (accountResult.success) {
                        // tmpUserInfo.Account__c = accountResult.id; // Field doesn't exist on Docebo_Users__c

                        const leadData = {
                            // Standard Lead fields
                            Company: tmpUserInfo.Organization_Name__c || "-",
                            Email: tmpUserInfo.Email__c,
                            Title: tmpUserInfo.Job_Title__c || "",
                            FirstName: tmpUserInfo.First_Name__c,
                            LastName: tmpUserInfo.Last_Name__c,
                            Website: tmpUserInfo.Organization_URL__c || "",
                            Status: "Open - Not Contacted",

                            // Custom fields from your exact field list
                            Created_by_Docebo_API__c: true,
                            GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
                            Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
                            Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
                            Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),
                            Contact_Type__c: "Other",
                            Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
                            Fax: getAdditionalData(user.userInfo.additional_fields || [], "26") || "", // Map from additional field if available
                            Salutation: getAdditionalData(user.userInfo.additional_fields || [], "27") || "", // Map from additional field if available
                            Phone: user.userInfo.user_data.phone || "", // FIX: Use actual phone from user_data
                            Languages__c: user.userInfo.user_data.language || "", // FIX: Use actual language from user_data
                            mailingcity__c: getAdditionalData(user.userInfo.additional_fields || [], "24") || "", // FIX: Correct field name
                            mailingcountry__c: getAdditionalData(user.userInfo.additional_fields || [], "28") || "", // FIX: Correct field name
                            mailingpostalcode__c: getAdditionalData(user.userInfo.additional_fields || [], "29") || "", // FIX: Correct field name
                            mailingstate__c: getStateLabel(user.userInfo.additional_fields || [], "25") || "", // FIX: Correct field name
                            mailingstreet__c: getAdditionalData(user.userInfo.additional_fields || [], "30") || "", // FIX: Correct field name
                            position_role__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c) || "", // FIX: Correct field name
                            LeadSource: "Docebo Platform",

                            // FIX: Add missing required fields for Lead (using correct field names)
                            AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
                            Industry: tmpUserInfo.Industry__c || "Not For Profit",
                            NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
                            Rating: tmpUserInfo.Rating__c || "Warm",
                            Time_Zone__c: user.userInfo.user_data.timezone || "",

                            Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
                            FTE__c: "Full-Time",
                            Gateway__c: "Docebo API",
                            Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
                            Legacy_Id__c: String(tmpUserInfo.User_Unique_Id__c || ""),
                            No_Longer_Leadership__c: false,
                            No_Longer_Staff__c: false,
                            Number_of_Years_in_the_Partnership__c: 0,
                            OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB",

                        };

                        try {
                            const leadResult = await conn.sobject("Lead").create(leadData);
                            if (leadResult.success) {
                                console.log(`Lead created successfully in batch: ${leadResult.id}`);

                                // Store the lead ID for later association (batch processing will handle this differently)
                                tmpUserInfo._leadId = leadResult.id; // Use a temporary field to store lead ID
                                console.log(`✅ Lead created in batch, will associate later: ${leadResult.id}`);
                            } else {
                                console.error("Lead creation failed in batch:", leadResult.errors);
                            }
                        } catch (leadError) {
                            console.error("Error creating lead in batch:", leadError);
                        }
                    }
                }

                // Remove the temporary _leadId field before adding to batch
                const leadId = tmpUserInfo._leadId;
                delete tmpUserInfo._leadId;

                batchData.push({
                    attributes: { type: "Docebo_Users__c" },
                    ...tmpUserInfo,
                    _leadId: leadId // Store it separately for later association
                });

            } catch (err) {
                console.error(`Error processing user ${user.userInfo?.user_data?.user_id}:`, err);
            }
        }

        try {
            const result = await conn.sobject("Docebo_Users__c").create(batchData, { allOrNone: false });

            result.forEach(async (res, index) => {
                if (res.success) {
                    console.log(`User ${batch[index].userInfo.user_data.user_id} saved successfully.`);

                    // Associate with Lead if one was created
                    const leadId = batchData[index]._leadId;
                    if (leadId) {
                        try {
                            // FIX: First verify the Lead exists and is accessible
                            const leadExists = await conn.sobject("Lead").findOne({ Id: leadId });
                            if (leadExists) {
                                const linkResult = await conn.sobject("Docebo_Users__c").update({
                                    Id: res.id,
                                    Lead__c: leadId
                                });

                                if (linkResult.success) {
                                    console.log(`✅ Batch: Docebo_Users__c ${res.id} linked to Lead ${leadId}`);
                                } else {
                                    console.error("❌ Batch: Failed to link Docebo_Users__c to Lead:", linkResult.errors);
                                    // Try alternative association
                                    const userEmail = batchData[index].Email__c;
                                    await tryAlternativeLeadAssociation(conn, res.id, userEmail);
                                }
                            } else {
                                console.log(`⚠️ Batch: Lead ${leadId} not found, trying email lookup`);
                                const userEmail = batchData[index].Email__c;
                                await tryAlternativeLeadAssociation(conn, res.id, userEmail);
                            }
                        } catch (linkError) {
                            console.error("❌ Batch: Error linking Docebo_Users__c to Lead:", linkError);
                            // Try alternative association
                            const userEmail = batchData[index].Email__c;
                            await tryAlternativeLeadAssociation(conn, res.id, userEmail);
                        }
                    }
                } else {
                    console.error(`Failed to save user ${batch[index].userInfo.user_data.user_id}:`, res.errors);
                }
            });
        } catch (err) {
            console.error("Error during batch save to Docebo_Users__c:", err);
        }
    }
}

let saveUserInfo = {
    // ONLY FIELDS THAT ACTUALLY EXIST ON DOCEBO_USERS__C:
    "User_Unique_Id__c": 0,
    "User_Level__c": "",
    "Deactivated__c": false,

    "User_Creation_Date__c": "",
    "User_Expiration_Date__c": "",
    "User_Last_Access_Date__c": "",
    "User_Suspension_Date__c": "",

    "Email_Validation_Status__c": true,

    "First_Name__c": "",
    "Last_Name__c": "",
    "Full_Name__c": "",
    "Email__c": "",

    "Organization_Name__c": "",
    "Organization_URL__c": "",
    "Organization_Headquarters__c": "",

    "Branch_Name__c": "",
    "Branch_Path__c": "",
    "Branches_Codes__c": 0,

    "Job_Title__c": "",
    "Employment_Type__c": "",
    "Role_Type__c": "",
    "Employment_Begin_Date__c": "",
    "Direct_Manager__c": "",

    "Backbone_Partner__c": false,
    "Back_Partner_Type__c": "",

    "Gender_Identity__c": "",
    "Race_Identity__c": "",

    "Initiative__c": "",
    "National_Regional_or_Local__c": "",

    // FIX: Add missing fields that exist on Docebo_Users__c
    "Languages__c": "",
    "Time_Zone__c": "",
    "Network_Partnership_Association__c": "",
    "City__c": "",
    "State__c": ""
}

function tidyData(userInfo, userListedInfo) {
    let tmpUserInfo = { ...saveUserInfo }; // Create a new object to avoid shared state
    let newUser = userInfo;
    let additionFields = ('additional_fields' in newUser) ? newUser.additional_fields : [];

    // Remove references to fields that don't exist on Docebo_Users__c
    // tmpUserInfo.Best_Describes_Your_Affiliation__c = null; // Field doesn't exist
    // tmpUserInfo.Network_Partnership_Association__c = null; // Field doesn't exist
    // tmpUserInfo.OwnerId = "005O400000BxnnxIAB"; // Field doesn't exist
    // tmpUserInfo.Partner_with_a_Member_of_StriveTogether__c = null; // Field doesn't exist
    // tmpUserInfo.StriveTogether_Network_Member__c = null; // Field doesn't exist
    // tmpUserInfo.User__c = null; // Field doesn't exist
    // tmpUserInfo.Who__c = null; // Field doesn't exist

    tmpUserInfo.Back_Partner_Type__c = getAdditionalData(additionFields, "16");
    tmpUserInfo.Backbone_Partner__c = getAdditionalData(additionFields, "15") === 'Yes';
    tmpUserInfo.Branch_Name__c = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].name : "";
    tmpUserInfo.Branch_Path__c = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].path : "";
    // Convert branch codes to numeric value or 0 if not a valid number
    const branchCodes = Array.isArray(newUser.branches) && newUser.branches.length > 0 ? newUser.branches[0].codes : "";
    tmpUserInfo.Branches_Codes__c = branchCodes && !isNaN(parseFloat(branchCodes)) ? parseFloat(branchCodes) : 0;
    tmpUserInfo.Direct_Manager__c = newUser.user_data.manager_username;
    tmpUserInfo.Email__c = newUser.user_data.email;
    tmpUserInfo.Email_Validation_Status__c = newUser.user_data.email_validation_status === '1';
    tmpUserInfo.Employment_Begin_Date__c = getAdditionalData(additionFields, "17");
    tmpUserInfo.Employment_Type__c = getAdditionalData(additionFields, "10");
    tmpUserInfo.First_Name__c = newUser.user_data.first_name;
    tmpUserInfo.Full_Name__c = `${newUser.user_data.first_name} ${newUser.user_data.last_name}`;
    tmpUserInfo.Gender_Identity__c = getAdditionalData(additionFields, "13");
    tmpUserInfo.Job_Title__c = getAdditionalData(additionFields, "8");
    tmpUserInfo.Initiative__c = getAdditionalData(additionFields, "20");
    tmpUserInfo.Last_Name__c = newUser.user_data.last_name;
    // tmpUserInfo.Level__c = newUser.user_data.level; // Field doesn't exist (use User_Level__c instead)
    tmpUserInfo.National_Regional_or_Local__c = getAdditionalData(additionFields, "21");
    tmpUserInfo.Organization_Name__c = getAdditionalData(additionFields, "14");
    tmpUserInfo.Organization_Headquarters__c = getAdditionalData(additionFields, "22");
    tmpUserInfo.Organization_URL__c = getAdditionalData(additionFields, "23") || ""; // Map from additional field if available
    tmpUserInfo.Race_Identity__c = getAdditionalData(additionFields, "12");
    tmpUserInfo.Role_Type__c = getAdditionalData(additionFields, "9");
    // FIX: Handle creation date properly with fallback to current date
    if (newUser.fired_at && newUser.fired_at !== "") {
        tmpUserInfo.User_Creation_Date__c = new Date(newUser.fired_at.replace(' ', 'T')).toISOString();
    } else {
        // Fallback to current date if fired_at is missing
        tmpUserInfo.User_Creation_Date__c = new Date().toISOString();
        console.log(`⚠️ No fired_at date provided for user ${tmpUserInfo.User_Unique_Id__c}, using current date`);
    }

    if (newUser.expiration_date && newUser.expiration_date !== "") {
        tmpUserInfo.User_Expiration_Date__c = new Date(newUser.expiration_date.replace(' ', 'T')).toISOString();
    } else {
        // Use creation date as expiration date if not provided
        tmpUserInfo.User_Expiration_Date__c = tmpUserInfo.User_Creation_Date__c;
    }
    tmpUserInfo.User_Level__c = newUser.user_data.level;
    tmpUserInfo.User_Unique_Id__c = parseInt(newUser.user_data.user_id, 10); // Ensure user_id is mapped correctly
    // tmpUserInfo.Username__c = newUser.user_data.username; // Field doesn't exist on Docebo_Users__c
    tmpUserInfo.Deactivated__c = newUser.user_data.valid === '1';
    tmpUserInfo.User_Last_Access_Date__c = (userListedInfo && userListedInfo.last_access_date) ? new Date(userListedInfo.last_access_date).toISOString() : null;
    tmpUserInfo.User_Suspension_Date__c = (newUser.user_data.valid === '0') ? new Date().toISOString() : null;

    // FIX: Add missing field mappings that exist on Docebo_Users__c
    tmpUserInfo.Languages__c = newUser.user_data.language || ""; // FIX: Map from user_data.language
    tmpUserInfo.Time_Zone__c = newUser.user_data.timezone || ""; // FIX: Map from user_data.timezone
    tmpUserInfo.Network_Partnership_Association__c = getAdditionalData(additionFields, "14") || ""; // FIX: Map from additional field
    tmpUserInfo.City__c = getAdditionalData(additionFields, "24") || ""; // FIX: Map from mailing city
    tmpUserInfo.State__c = getStateLabel(additionFields, "25") || ""; // FIX: Map from mailing state

    // All additional fields removed - they don't exist on Docebo_Users__c
    // These fields are only used for Lead creation, not Docebo_Users__c
    // tmpUserInfo.Languages__c = newUser.user_data.language || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Phone__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Fax__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Salutation__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingStreet__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingCity__c = getAdditionalData(additionFields, "24") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingState__c = getStateLabel(additionFields, "25") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingPostalCode__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.MailingCountry__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Website__c = getAdditionalData(additionFields, "23") || ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Industry__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.AnnualRevenue__c = 0; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.NumberOfEmployees__c = 0; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Rating__c = ""; // Field doesn't exist on Docebo_Users__c
    // tmpUserInfo.Position_Role__c = tmpUserInfo.Role_Type__c || ""; // Field doesn't exist on Docebo_Users__c

    // Enhanced logging for processed data
    console.log(`🔧 PROCESSED USER DATA for User ${tmpUserInfo.User_Unique_Id__c}:`);
    console.log("Processed data from tidyData function:", JSON.stringify(tmpUserInfo, null, 2));

    // Log specific field mappings for debugging - only fields that exist on Docebo_Users__c
    console.log("📊 KEY FIELD MAPPINGS:");
    console.log(`   Organization_Name__c: "${tmpUserInfo.Organization_Name__c}"`);
    console.log(`   Job_Title__c: "${tmpUserInfo.Job_Title__c}"`);
    console.log(`   Role_Type__c: "${tmpUserInfo.Role_Type__c}"`);
    console.log(`   Race_Identity__c: "${tmpUserInfo.Race_Identity__c}"`);
    console.log(`   Gender_Identity__c: "${tmpUserInfo.Gender_Identity__c}"`);
    console.log(`   Initiative__c: "${tmpUserInfo.Initiative__c}"`);
    console.log(`   National_Regional_or_Local__c: "${tmpUserInfo.National_Regional_or_Local__c}"`);

    // FIX: Log the newly added field mappings
    console.log(`   Languages__c: "${tmpUserInfo.Languages__c}"`);
    console.log(`   Time_Zone__c: "${tmpUserInfo.Time_Zone__c}"`);
    console.log(`   Network_Partnership_Association__c: "${tmpUserInfo.Network_Partnership_Association__c}"`);
    console.log(`   City__c: "${tmpUserInfo.City__c}"`);
    console.log(`   State__c: "${tmpUserInfo.State__c}"`);

    return tmpUserInfo;
}

const getAdditionalData = (additionalArr, fieldId) => {
    let optionLabel = "";
    additionalArr.forEach(element => {
        if (element.id == fieldId) {
            if (element.enabled == true) {
                if ('options' in element) {
                    element.options.forEach(elOpt => {
                        if (elOpt.id == element.value) {
                            optionLabel = elOpt.label;
                        }
                    })
                } else {
                    optionLabel = element.value;
                }
            }
        }
    });
    return optionLabel;
}

// Helper function specifically for state dropdown (ID 25) - needed for Lead creation
const getStateLabel = (additionalArr, fieldId) => {
    let stateLabel = "";
    additionalArr.forEach(element => {
        if (element.id == fieldId) {
            if (element.enabled == true && element.value && element.value !== "null") {
                if ('options' in element) {
                    element.options.forEach(elOpt => {
                        if (elOpt.id == element.value) {
                            stateLabel = elOpt.label;
                        }
                    })
                }
            }
        }
    });
    return stateLabel;
}

// Helper function to try alternative Lead association when direct linking fails
async function tryAlternativeLeadAssociation(conn, doceboUserId, email) {
    if (!email) {
        console.log(`⚠️ No email provided for alternative Lead association`);
        return;
    }

    try {
        console.log(`🔍 Trying alternative Lead association for email: ${email}`);

        // Find Leads with matching email
        const leads = await conn.sobject("Lead")
            .find({
                Email: email,
                IsConverted: false // Only unconverted leads
            })
            .limit(5)
            .execute();

        if (leads.length > 0) {
            console.log(`📋 Found ${leads.length} potential Lead(s) for association`);

            // Try each Lead until one works
            for (const lead of leads) {
                try {
                    const linkResult = await conn.sobject("Docebo_Users__c").update({
                        Id: doceboUserId,
                        Lead__c: lead.Id
                    });

                    if (linkResult.success) {
                        console.log(`✅ Alternative association successful: Docebo_Users__c ${doceboUserId} linked to Lead ${lead.Id}`);
                        return true;
                    } else {
                        console.log(`⚠️ Alternative Lead ${lead.Id} failed:`, linkResult.errors);
                    }
                } catch (altError) {
                    console.log(`⚠️ Alternative Lead ${lead.Id} error: ${altError.message}`);
                }
            }

            console.log(`❌ All alternative Lead associations failed for ${email}`);
        } else {
            console.log(`⚠️ No unconverted Leads found with email: ${email}`);
        }
    } catch (error) {
        console.log(`❌ Error in alternative Lead association: ${error.message}`);
    }

    return false;
}

module.exports = {
    createNewUser,
    getTotalUserList,
    saveUsersInBatch,
    tidyData
};