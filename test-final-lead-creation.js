require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Mock user data with comprehensive fields
const mockUserInfo = {
    user_data: {
        user_id: "66666",
        first_name: "<PERSON>",
        last_name: "Final Lead Test",
        email: "<EMAIL>",
        username: "michael_final_lead",
        level: "Administrator",
        manager_username: "michael_manager",
        email_validation_status: "1",
        valid: "1"
    },
    additional_fields: [
        { id: "8", value: "Chief Technology Officer", enabled: true }, // Job Title
        { id: "9", value: "Executive", enabled: true }, // Role Type
        { id: "10", value: "Full-time", enabled: true }, // Employment Type
        { id: "12", value: "White", enabled: true }, // Race Identity
        { id: "13", value: "Male", enabled: true }, // Gender Identity
        { id: "14", value: "Tech Innovation Partners", enabled: true }, // Organization Name
        { id: "15", value: "No", enabled: true }, // Backbone Partner
        { id: "16", value: "Technology Partner", enabled: true }, // Back Partner Type
        { id: "17", value: "2019-06-01", enabled: true }, // Employment Begin Date
        { id: "20", value: "Digital Innovation", enabled: true }, // Initiative
        { id: "21", value: "Regional", enabled: true }, // National/Regional/Local
        { id: "22", value: "Austin, TX", enabled: true } // Organization Headquarters
    ],
    branches: [
        {
            name: "Technology Innovation Branch",
            path: "/tech/innovation",
            codes: "54321"
        }
    ],
    fired_at: "2019-06-01 09:00:00",
    expiration_date: "2025-12-31 23:59:59"
};

const mockUserListedInfo = {
    last_access_date: "2024-01-30T12:15:00Z"
};

async function testFinalLeadCreation() {
    try {
        console.log('🧪 Testing final lead creation with your exact field mapping...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up any existing test records
        console.log('\n🧹 Cleaning up any existing test records...');
        
        try {
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }

            const existingContacts = await conn.sobject("Contact")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const contact of existingContacts) {
                await conn.sobject("Contact").delete(contact.Id);
                console.log(`   Deleted existing Contact: ${contact.Id}`);
            }

        } catch (cleanupError) {
            console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
        }

        console.log('\n🚀 Creating new user with final lead mapping...');
        
        // Create the user (which should trigger lead creation)
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('✅ User creation completed successfully');
            
            // Verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockUserInfo.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                console.log(`   Job Title: ${createdUser.Job_Title__c}`);
            }

            // Check Lead record with the exact fields from your mapping
            const fieldList = [
                'Id', 'FirstName', 'LastName', 'Email', 'Company', 'Title', 'Website', 'Status',
                'Created_by_Docebo_API__c', 'Gender__c', 'Role_Type__c', 'Employment_Type__c', 'Race__c',
                'Contact_Type__c', 'Description', 'Fax', 'Salutation', 'Phone', 'Languages__c',
                'MailingCity__c', 'MailingCountry__c', 'MailingPostalCode__c', 'MailingState__c', 'MailingStreet__c',
                'Position_Role__c', 'Annual_Revenue__c', 'Industry__c', 'LeadSource', 'NumberOfEmployees__c',
                'Rating__c', 'Time_Zone__c', 'accountid', 'Active_Portal_User__c', 'FTE__c', 'Gateway__c',
                'Inactive_Contact__c', 'Legacy_Id__c', 'No_Longer_Leadership__c', 'No_Longer_Staff__c',
                'Number_of_Years_in_the_Partnership__c', 'OwnerId', 'Type__c'
            ].join(', ');

            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockUserInfo.user_data.email }, fieldList);
            
            if (createdLead) {
                console.log(`✅ Lead created: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Status: ${createdLead.Status}`);
                console.log(`   Title: ${createdLead.Title}`);
                
                console.log('\n📋 Your Exact Field Mapping Results:');
                console.log('=' .repeat(60));
                
                // Standard fields
                console.log('\n📝 Standard Lead Fields:');
                console.log(`   Company: ${createdLead.Company || 'N/A'}`);
                console.log(`   Email: ${createdLead.Email || 'N/A'}`);
                console.log(`   Title: ${createdLead.Title || 'N/A'}`);
                console.log(`   FirstName: ${createdLead.FirstName || 'N/A'}`);
                console.log(`   LastName: ${createdLead.LastName || 'N/A'}`);
                console.log(`   Website: ${createdLead.Website || 'N/A'}`);
                console.log(`   Status: ${createdLead.Status || 'N/A'}`);
                
                // Custom fields from your mapping
                console.log('\n🔧 Custom Fields:');
                console.log(`   Created_by_Docebo_API__c: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                console.log(`   Gender__c: ${createdLead.Gender__c || 'N/A'}`);
                console.log(`   Role_Type__c: ${createdLead.Role_Type__c || 'N/A'}`);
                console.log(`   Employment_Type__c: ${createdLead.Employment_Type__c || 'N/A'}`);
                console.log(`   Race__c: ${createdLead.Race__c || 'N/A'}`);
                console.log(`   Contact_Type__c: ${createdLead.Contact_Type__c || 'N/A'}`);
                console.log(`   Description: ${createdLead.Description || 'N/A'}`);
                console.log(`   Position_Role__c: ${createdLead.Position_Role__c || 'N/A'}`);
                console.log(`   LeadSource: ${createdLead.LeadSource || 'N/A'}`);
                
                // System fields
                console.log('\n⚙️ System Fields:');
                console.log(`   accountid: ${createdLead.accountid || 'N/A'}`);
                console.log(`   Active_Portal_User__c: ${createdLead.Active_Portal_User__c || 'N/A'}`);
                console.log(`   Gateway__c: ${createdLead.Gateway__c || 'N/A'}`);
                console.log(`   Legacy_Id__c: ${createdLead.Legacy_Id__c || 'N/A'}`);
                console.log(`   Type__c: ${createdLead.Type__c || 'N/A'}`);
                console.log(`   OwnerId: ${createdLead.OwnerId || 'N/A'}`);
                
                console.log('\n🎯 SUCCESS! Lead created with your exact field mapping!');
                console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${createdLead.Id}`);
                
            } else {
                console.log('❌ No Lead record found');
            }

        } else {
            console.log('❌ User creation failed');
        }

    } catch (error) {
        console.error('💥 Error in final lead creation test:', error);
        
        if (error.message && error.message.includes('INVALID_FIELD')) {
            console.log('\n💡 Field Error Details:');
            console.log('   Error:', error.message);
            console.log('   Some fields may not exist on the Lead object yet.');
            console.log('   These fields need to be created in Salesforce first.');
        }
    }
}

// Execute the test
console.log('🔄 Starting final lead creation test...');
testFinalLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
