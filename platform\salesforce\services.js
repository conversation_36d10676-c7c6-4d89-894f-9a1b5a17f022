const getConnection = require('./common/getConnection');

async function createIltSessionEnrollment(enrollmentData) {
    // Implement the function to create ILT session enrollment in Salesforce
    // This is a placeholder implementation; adjust as needed
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection in createIltSessionEnrollment");
            return false;
        }
        const enrollmentRecord = {
            Enrollment_ID__c: `UE-${enrollmentData.course_id}-${enrollmentData.user_id}`,
            Course__c: enrollmentData.course_id, // FIX: Use Course__c instead of Course_Id__c
            Docebo_User__c: enrollmentData.user_id, // FIX: Use Docebo_User__c instead of User_Id__c
            Completion__c: enrollmentData.completion || 0,
            Completion_Date__c: enrollmentData.completion_date ? new Date(enrollmentData.completion_date).toISOString() : null,
            Score__c: enrollmentData.score || 0
        };
        const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ Enrollment_ID__c: enrollmentRecord.Enrollment_ID__c });
        if (existingEnrollment) {
            enrollmentRecord.Id = existingEnrollment.Id;
            const updateResult = await conn.sobject("Docebo_CourseEnrollment__c").update(enrollmentRecord);
            return updateResult.success;
        } else {
            const createResult = await conn.sobject("Docebo_CourseEnrollment__c").create(enrollmentRecord);
            return createResult.success;
        }
    } catch (err) {
        console.error("Error in createIltSessionEnrollment:", err);
        return false;
    }
};

const { createCourseEnrollment, saveCourseEnrollmentsInBatch } = require("./courseEnrollment/createCourseEnrollment");
const { deleteCourseEnrollment } = require("./courseEnrollment/deleteCourseEnrollment");
const { deleteIltSessionEnrollment } = require("./session/deleteSessionEnrollment");
const { learningPlanEnrollment, learningPlanEnrollmentCompleted } = require("./LearningPlan/createLearningPlanEnrollment");
const { updateILTSession } = require("./session/updateSession");
const { createNewSession } = require("./session/createSession");

// Both deleteCourseEnrollment and deleteIltSessionEnrollment functions are now imported from dedicated files

module.exports = {
    createIltSessionEnrollment,
    deleteIltSessionEnrollment,
    updateCourseEnrollment: createCourseEnrollment,
    deleteCourseEnrollment,
    learningPlanEnrollment,
    learningPlanEnrollmentCompleted,
    updateILTSession,
    createNewSession
};