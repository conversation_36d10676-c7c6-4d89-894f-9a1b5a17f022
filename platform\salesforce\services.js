const getConnection = require('./common/getConnection');

async function createIltSessionEnrollment(enrollmentData) {
    // Implement the function to create ILT session enrollment in Salesforce
    // This is a placeholder implementation; adjust as needed
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection in createIltSessionEnrollment");
            return false;
        }

        // FIX: Need to get the Salesforce Course ID, not use the Docebo course_id directly
        const courseRecord = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: enrollmentData.course_id });

        if (!courseRecord) {
            console.error(`Course not found in Salesforce with External ID: ${enrollmentData.course_id}`);
            return false;
        }

        // FIX: Need to get the Salesforce User ID, not use the Docebo user_id directly
        const userRecord = await conn.sobject("Docebo_Users__c")
            .findOne({ User_Unique_Id__c: enrollmentData.user_id });

        if (!userRecord) {
            console.error(`User not found in Salesforce with Unique ID: ${enrollmentData.user_id}`);
            return false;
        }

        const enrollmentRecord = {
            Enrollment_ID__c: `UE-${enrollmentData.course_id}-${enrollmentData.user_id}`,
            Course__c: courseRecord.Id, // FIX: Use Salesforce Course ID, not Docebo course_id
            Docebo_User__c: userRecord.Id, // FIX: Use Salesforce User ID, not Docebo user_id
            Completion__c: enrollmentData.completion || 0,
            Completion_Date__c: enrollmentData.completion_date ? new Date(enrollmentData.completion_date).toISOString() : null,
            Score__c: enrollmentData.score || 0
        };
        const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ Enrollment_ID__c: enrollmentRecord.Enrollment_ID__c });
        if (existingEnrollment) {
            enrollmentRecord.Id = existingEnrollment.Id;
            const updateResult = await conn.sobject("Docebo_CourseEnrollment__c").update(enrollmentRecord);
            return updateResult.success;
        } else {
            const createResult = await conn.sobject("Docebo_CourseEnrollment__c").create(enrollmentRecord);
            return createResult.success;
        }
    } catch (err) {
        console.error("Error in createIltSessionEnrollment:", err);
        return false;
    }
};

const { createCourseEnrollment, saveCourseEnrollmentsInBatch } = require("./courseEnrollment/createCourseEnrollment");
const { deleteCourseEnrollment } = require("./courseEnrollment/deleteCourseEnrollment");
const { deleteIltSessionEnrollment } = require("./session/deleteSessionEnrollment");
const { learningPlanEnrollment, learningPlanEnrollmentCompleted } = require("./LearningPlan/createLearningPlanEnrollment");
const { updateILTSession } = require("./session/updateSession");
const { createNewSession } = require("./session/createSession");

// Both deleteCourseEnrollment and deleteIltSessionEnrollment functions are now imported from dedicated files

module.exports = {
    createIltSessionEnrollment,
    deleteIltSessionEnrollment,
    updateCourseEnrollment: createCourseEnrollment,
    deleteCourseEnrollment,
    learningPlanEnrollment,
    learningPlanEnrollmentCompleted,
    updateILTSession,
    createNewSession
};