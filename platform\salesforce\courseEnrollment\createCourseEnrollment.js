const getConnection = require("../common/getConnection");
const getSalesForceUserId = require("../common/getSalesForceUserId");
const { createNewUser, tidyData } = require("../users/createUser");
const doceboService = require("../../docebo/services");

const courseEnrollmentTemplate = {
    Course_Id__c: "",
    User_Id__c: "",
    Enrollment_Date__c: "",
    Completion_Date__c: "",
    Status__c: "",
    Score__c: 0,
    External_Id__c: ""  // Composite key: courseId-userId
};

async function getCourseEnrollmentSalesForceId(courseId, userId) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return null;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getCourseEnrollmentSalesForceId");
        return null;
    }
    
    const externalId = `${courseId}-${userId}`;
    
    try {
        const record = await conn.sobject("Docebo_Course_Enrollment__c")
            .findOne({ External_Id__c: externalId });
            
        if (!record) {
            console.log("Course enrollment doesn't exist in Salesforce");
            return null;
        }
        
        console.log(`Course enrollment found. ID: ${record.Id}`);
        return record.Id;
    } catch (err) {
        console.error("Error finding course enrollment:", err);
        return null;
    }
}

async function createCourseEnrollment(enrollmentData) {
    // Check if enrollment already exists
    const existingId = await getCourseEnrollmentSalesForceId(
        enrollmentData.course_id,
        enrollmentData.user_id
    );
    
    if (existingId) {
        console.log("Course enrollment already exists, skipping creation");
        return true;
    }
    
    // Get Salesforce User ID
    let userId = await getSalesForceUserId(enrollmentData.user_id);
    
    // If user doesn't exist, try to create them
    if (!userId) {
        console.log(`User ${enrollmentData.user_id} not found in Salesforce, attempting to create...`);
        
        try {
            // Fetch user data from Docebo
            const userResponse = await doceboService.getUserInfo(enrollmentData.user_id);
            const userListedResponse = await doceboService.getUserListedInfo(enrollmentData.user_id);
            
            if (userResponse && userResponse.data && userListedResponse && userListedResponse.data) {
                // Create the user in Salesforce
                const userCreated = await createNewUser(userResponse.data, userListedResponse.data);
                
                if (userCreated) {
                    console.log(`User ${enrollmentData.user_id} created successfully`);
                    // Get the newly created user's Salesforce ID
                    userId = await getSalesForceUserId(enrollmentData.user_id);
                } else {
                    console.error(`Failed to create user ${enrollmentData.user_id}`);
                }
            } else {
                console.error(`Could not retrieve user data for ${enrollmentData.user_id} from Docebo`);
            }
        } catch (err) {
            console.error(`Error creating user ${enrollmentData.user_id}:`, err);
        }
        
        // If still no user ID, we can't proceed
        if (!userId) {
            console.error(`User still not found in Salesforce after creation attempt: ${enrollmentData.user_id}`);
            return false;
        }
    }
    
    // Create record in Salesforce
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in createCourseEnrollment");
        return false;
    }
    
    // Verify course exists in Salesforce
    try {
        const courseRecord = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: enrollmentData.course_id });
            
        if (!courseRecord) {
            console.error(`Course doesn't exist in Salesforce: ${enrollmentData.course_id}. Cannot create enrollment.`);
            return false;
        }
        
        // Prepare enrollment record with the Salesforce Course ID
        const enrollment = { ...courseEnrollmentTemplate };
        enrollment.Course_Id__c = courseRecord.Id; // Use Salesforce Course ID
        enrollment.User_Id__c = userId;
        enrollment.Enrollment_Date__c = enrollmentData.enrollment_date ?
            new Date(enrollmentData.enrollment_date.replace(' ', 'T')).toISOString() : "";
        enrollment.Completion_Date__c = enrollmentData.completion_date ?
            new Date(enrollmentData.completion_date.replace(' ', 'T')).toISOString() : "";
        enrollment.Status__c = enrollmentData.status || "Enrolled";
        enrollment.Score__c = enrollmentData.score || 0;
        enrollment.External_Id__c = `${enrollmentData.course_id}-${enrollmentData.user_id}`;
        
        // Create/update the enrollment
        const result = await conn.sobject("Docebo_Course_Enrollment__c")
            .upsert(enrollment, "External_Id__c");
        return result.success;
    } catch (err) {
        console.error(`Error verifying course or creating enrollment for ${enrollmentData.course_id}:`, err);
        return false;
    }
}

// Helper function to delay execution
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper function to retry an operation with exponential backoff
async function retryOperation(operation, maxRetries = 5, initialDelay = 1000) {
    let retries = 0;
    while (true) {
        try {
            return await operation();
        } catch (err) {
            // Handle NOT_FOUND errors specially
            if (err.errorCode === 'NOT_FOUND') {
                console.error('NOT_FOUND error during operation:', err.message);
                
                // Create a more informative error for debugging
                const enhancedError = new Error(`NOT_FOUND: ${err.message} - This usually occurs when a referenced record doesn't exist in Salesforce.`);
                enhancedError.originalError = err;
                enhancedError.errorCode = err.errorCode;
                
                throw enhancedError;
            }
            
            // Rate limit handling
            if (retries >= maxRetries ||
                !(err.message?.includes('Exceeded max limit of concurrent call') ||
                  err.errorCode === 'REQUEST_LIMIT_EXCEEDED')) {
                throw err; // If it's not a rate limit error or we've retried too many times, rethrow
            }
            
            const delayTime = initialDelay * Math.pow(2, retries);
            console.log(`Rate limit hit. Retrying after ${delayTime}ms (retry ${retries + 1}/${maxRetries})...`);
            await delay(delayTime);
            retries++;
        }
    }
}

async function saveCourseEnrollmentsInBatch(enrollments) {
    // Reduce batch size to limit API calls
    const batchSize = 50; // Reduced from 200 to 50
    const conn = await getConnection();
    
    if (!conn || !conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return false;
    }
    
    for (let i = 0; i < enrollments.length; i += batchSize) {
        const batch = enrollments.slice(i, i + batchSize);
        console.log(`Processing batch ${i / batchSize + 1}: ${batch.length} enrollments`);
        
        const batchData = [];
        
        // Process users and courses to prepare enrollment data
        for (const enrollment of batch) {
            try {
                // Get Salesforce User ID
                let userId = await getSalesForceUserId(enrollment.user_id);
                
                // If user doesn't exist, try to create them
                if (!userId) {
                    console.log(`User ${enrollment.user_id} not found in Salesforce, attempting to create...`);
                    
                    try {
                        // Fetch user data from Docebo
                        const userResponse = await doceboService.getUserInfo(enrollment.user_id);
                        const userListedResponse = await doceboService.getUserListedInfo(enrollment.user_id);
                        
                        if (userResponse && userResponse.data && userListedResponse && userListedResponse.data) {
                            // Create the user in Salesforce
                            const userCreated = await createNewUser(userResponse.data, userListedResponse.data);
                            
                            if (userCreated) {
                                console.log(`User ${enrollment.user_id} created successfully`);
                                // Get the newly created user's Salesforce ID
                                userId = await getSalesForceUserId(enrollment.user_id);
                            } else {
                                console.error(`Failed to create user ${enrollment.user_id}`);
                            }
                        } else {
                            console.error(`Could not retrieve user data for ${enrollment.user_id} from Docebo`);
                        }
                    } catch (err) {
                        console.error(`Error creating user ${enrollment.user_id}:`, err);
                    }
                    
                    // If still no user ID, skip this enrollment
                    if (!userId) {
                        console.error(`User still not found in Salesforce after creation attempt: ${enrollment.user_id}`);
                        continue;
                    }
                }
                
                // Verify course exists in Salesforce
                try {
                    const courseRecord = await conn.sobject("Docebo_Course__c")
                        .findOne({ Course_External_Id__c: enrollment.course_id });
                        
                    if (!courseRecord) {
                        console.error(`Course doesn't exist in Salesforce: ${enrollment.course_id}. Skipping enrollment for user ${enrollment.user_id}`);
                        continue;
                    }
                    
                    // Use the Salesforce Course ID instead of the Docebo course ID
                    // Prepare enrollment record
                    const enrollmentRecord = { ...courseEnrollmentTemplate };
                    enrollmentRecord.Course_Id__c = courseRecord.Id; // Use Salesforce Course ID
                    enrollmentRecord.User_Id__c = userId;
                    enrollmentRecord.Enrollment_Date__c = enrollment.enrollment_date ?
                        new Date(enrollment.enrollment_date.replace(' ', 'T')).toISOString() : "";
                    enrollmentRecord.Completion_Date__c = enrollment.completion_date ?
                        new Date(enrollment.completion_date.replace(' ', 'T')).toISOString() : "";
                    enrollmentRecord.Status__c = enrollment.status || "Enrolled";
                    enrollmentRecord.Score__c = enrollment.score || 0;
                    enrollmentRecord.External_Id__c = `${enrollment.course_id}-${enrollment.user_id}`;
                    
                    batchData.push({
                        attributes: { type: "Docebo_Course_Enrollment__c" },
                        ...enrollmentRecord
                    });
                } catch (err) {
                    console.error(`Error verifying course ${enrollment.course_id}:`, err);
                    continue;
                }
            } catch (err) {
                console.error(`Error processing enrollment for user ${enrollment.user_id}:`, err);
            }
        }
        
        // If batch is empty, skip to next batch
        if (batchData.length === 0) {
            console.log("No valid enrollments in this batch, skipping...");
            continue;
        }
        
        // Process in smaller chunks to avoid rate limits
        const chunkSize = 10; // Process 10 records at a time
        for (let j = 0; j < batchData.length; j += chunkSize) {
            const chunk = batchData.slice(j, j + chunkSize);
            console.log(`Processing chunk ${j / chunkSize + 1} of batch ${i / batchSize + 1}: ${chunk.length} enrollments`);
            
            try {
                // Verify that all referenced objects exist before attempting upsert
                for (const record of chunk) {
                    // Verify Course ID exists
                    try {
                        const courseExists = await conn.sobject("Docebo_Course__c")
                            .retrieve(record.Course_Id__c);
                        
                        if (!courseExists || !courseExists.Id) {
                            console.error(`Course with ID ${record.Course_Id__c} does not exist in Salesforce. Skipping enrollment.`);
                            // Remove this record from the chunk
                            chunk.splice(chunk.indexOf(record), 1);
                            continue;
                        }
                        
                        // Verify User ID exists
                        const userExists = await conn.sobject("Docebo_Users__c")
                            .retrieve(record.User_Id__c);
                        
                        if (!userExists || !userExists.Id) {
                            console.error(`User with ID ${record.User_Id__c} does not exist in Salesforce. Skipping enrollment.`);
                            // Remove this record from the chunk
                            chunk.splice(chunk.indexOf(record), 1);
                            continue;
                        }
                    } catch (verifyErr) {
                        console.error(`Error verifying references for enrollment:`, verifyErr);
                        // Remove this record from the chunk
                        chunk.splice(chunk.indexOf(record), 1);
                    }
                }
                
                // Skip if all records were invalid
                if (chunk.length === 0) {
                    console.log("All records in this chunk were invalid. Skipping upsert.");
                    continue;
                }
                
                // Add retry logic with exponential backoff
                const result = await retryOperation(async () => {
                    return await conn.sobject("Docebo_Course_Enrollment__c")
                        .upsert(chunk, "External_Id__c");
                });
                
                result.forEach((res, index) => {
                    if (res.success) {
                        const originalIndex = j + index;
                        const userIndex = originalIndex < batch.length ? originalIndex : -1;
                        const userId = userIndex >= 0 ? batch[userIndex].user_id : 'unknown';
                        console.log(`Enrollment for user ${userId} saved successfully.`);
                    } else {
                        console.error(`Failed to save enrollment:`, res.errors);
                    }
                });
                
                // Add a small delay between chunks to avoid hitting rate limits
                await delay(1000);
                
            } catch (err) {
                // Detailed error logging to better diagnose issues
                console.error("Error during chunk save:", err);
                
                if (err.errorCode === 'NOT_FOUND') {
                    console.error("This is likely caused by a reference to a non-existent record in Salesforce.");
                    console.error("Enrollment details:", JSON.stringify(chunk, null, 2));
                    
                    // Try to identify which record is causing the issue
                    try {
                        if (chunk.length === 1) {
                            console.error(`Problem record: Course_Id__c=${chunk[0].Course_Id__c}, User_Id__c=${chunk[0].User_Id__c}`);
                        } else {
                            console.error(`Multiple records in chunk. Consider processing one at a time for diagnosis.`);
                        }
                    } catch (logErr) {
                        console.error("Error while logging detailed error information:", logErr);
                    }
                }
            }
        }
    }
    
    return true;
}

module.exports = {
    createCourseEnrollment,
    saveCourseEnrollmentsInBatch,
    getCourseEnrollmentSalesForceId
};