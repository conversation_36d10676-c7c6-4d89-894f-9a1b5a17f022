const axios = require('axios');
const doceboService = require("./services")
const salesforceService = require("../salesforce/services")
// const createNewUser  = require("../salesforce/users/createUser");
const { createNewUser } = require("../salesforce/users/createUser");
const { deleteUser } = require("../salesforce/users/deleteUser");
const { updateUser } = require("../salesforce/users/updateUser");
const {
    getUserCourseEnrollments,
    getUserEnrollmentsWithCourseDetails,
    getUserEnrollmentsSummary
} = require("./getUserEnrollments");

let messageQue = [];

exports.userManagement = async (req, res) => {
    const payload = req.body;
    if (!messageQue.includes(payload.message_id)) {
        messageQue.push(payload.message_id);
        switch (payload.event) {
            case 'user.created':
                console.log("======= User Created =======");
                console.log(payload);
                if (payload.payloads) {
                    for (let i = 0; i < payload.payloads.length; i++) {
                        let userInfo = await doceboService.getUserInfo(payload.payloads[i].user_id);
                        if (userInfo.status === 200) {
                            let userData = userInfo.data;
                            let userListedInfo = await doceboService.getUserListedInfo(payload.payloads[i].user_id);
                            userData["fired_at"] = payload.payloads[i].fired_at;
                            userData["expiration_date"] = payload.payloads[i].expiration_date || payload.payloads[i].fired_at;

                            // Enhanced logging for webhook data
                            console.log(`📥 WEBHOOK USER DATA for User ${payload.payloads[i].user_id}:`);
                            console.log("Raw userData from Docebo API:", JSON.stringify(userData, null, 2));
                            console.log("Raw userListedInfo:", JSON.stringify(userListedInfo, null, 2));
                            console.log("Webhook payload data:", JSON.stringify(payload.payloads[i], null, 2));

                            let saveRes = await createNewUser(userData, userListedInfo);
                            console.log("Multi users creation result: ", saveRes);
                        }
                    }
                } else {
                    let userInfo = await doceboService.getUserInfo(payload.payload.user_id);
                    let userListedInfo = await doceboService.getUserListedInfo(payload.payload.user_id);
                    if (userInfo.status == 200) {
                        let userData = userInfo.data;
                        userData["fired_at"] = payload.payload.fired_at;
                        userData["expiration_date"] = payload.payload.expiration_date || payload.payload.fired_at;

                        // Enhanced logging for single user webhook data
                        console.log(`📥 SINGLE USER WEBHOOK DATA for User ${payload.payload.user_id}:`);
                        console.log("Raw userData from Docebo API:", JSON.stringify(userData, null, 2));
                        console.log("Raw userListedInfo:", JSON.stringify(userListedInfo, null, 2));
                        console.log("Webhook payload data:", JSON.stringify(payload.payload, null, 2));

                        let saveRes = await createNewUser(userData, userListedInfo);
                        console.log("Single user creation result: ", saveRes);
                    }
                }
                break;
            case 'user.updated':
                let updatedUserInfo = await doceboService.getUserInfo(payload.payload.user_id);
                let userListedInfo = await doceboService.getUserListedInfo(payload.payload.user_id);
                console.log("======= User Updated =======");
                console.log(payload);
                if (updatedUserInfo.status == 200) {
                    let userData = updatedUserInfo.data;
                    userData["fired_at"] = payload.payload.fired_at;
                    userData["expiration_date"] = payload.payload.fired_at;
                    userData["update_date"] = payload.payload.update_date;
                    let updateRes = await updateUser(userData, userListedInfo);
                    console.log(updateRes)
                }
                break;
            case 'user.deleted':
                console.log("======= User Deleted =======");
                console.log(payload);
                if (payload.payloads) {
                    for (let i = 0; i < payload.payloads.length; i++) {
                        let delRes = await deleteUser(payload.payloads[i].user_id);
                        console.log("Multi user deletion result: ", delRes);
                    }
                } else {
                    if (payload.payload.user_id) {
                        let delRes = await deleteUser(payload.payload.user_id);
                        console.log("Single user deletion result: ", delRes);
                    }
                }
                break;
            case "user.selfregistrationrequest.approved":
                if (payload.payloads) {
                    for (let i = 0; i < payload.payloads.length; i++) {
                        let userInfo = await doceboService.getUserInfo(
                            payload.payloads[i].user_id
                        );
                        if (userInfo.status === 200) {
                            let userData = userInfo.data;
                            let userListedInfo = await doceboService.getUserListedInfo(
                                payload.payloads[i].user_id
                            );
                            userData["fired_at"] = payload.payloads[i].fired_at;
                            userData["expiration_date"] = payload.payloads[i].expiration_date || payload.payloads[i].fired_at;
                            let saveRes = await createNewUser(userData, userListedInfo);
                            console.log("Multi users creation result: ", saveRes);
                        }
                    }
                } else {
                    let userInfo = await doceboService.getUserInfo(
                        payload.payload.user_id
                    );
                    let userListedInfo = await doceboService.getUserListedInfo(
                        payload.payload.user_id
                    );
                    if (userInfo.status == 200) {
                        let userData = userInfo.data;
                        userData["fired_at"] = payload.payload.fired_at;
                        userData["expiration_date"] = payload.payload.expiration_date || payload.payload.fired_at;
                        let saveRes = await createNewUser(userData, userListedInfo);
                        console.log("Single user creation result: ", saveRes);
                    }
                }
                break;
            default:
                break;
        }
    }
    res.status(200).send({ status: "success", message: "New user created" });
}

// Queue to store session management tasks for processing after response is sent
const sessionManagementQueue = [];

// Flag to prevent multiple queue processing at the same time
let isProcessingSessionQueue = false;

// Function to process the session management queue in the background
async function processSessionManagementQueue() {
    if (sessionManagementQueue.length === 0 || isProcessingSessionQueue) return;
    
    isProcessingSessionQueue = true;
    console.log(`Processing ${sessionManagementQueue.length} queued session management tasks`);
    
    try {
        // Process one item at a time due to complexity of operations
        const task = sessionManagementQueue.shift();
        const payload = task.payload;
        
        try {
            switch (payload.event) {
                case 'ilt.session.created':
                    console.log(`🎯 SESSION CREATION WEBHOOK - Processing ${payload.payloads ? payload.payloads.length : 1} session(s)`);

                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                console.log(`📋 WEBHOOK PAYLOAD ${i + 1}:`, JSON.stringify(payload.payloads[i], null, 2));

                                let courseInfo = await doceboService.getCourseInfo(payload.payloads[i].course_id);
                                let sessionInfo = await doceboService.getCourseSessionInfo(payload.payloads[i].session_id);

                                console.log(`📡 Session API Response Keys: ${sessionInfo?.data ? Object.keys(sessionInfo.data).join(', ') : 'No data'}`);
                                console.log(`📡 Session has instructor_id: ${sessionInfo?.data?.instructor_id ? 'YES' : 'NO'}`);
                                console.log(`📡 Session has instructors: ${sessionInfo?.data?.instructors ? 'YES' : 'NO'}`);

                                if (courseInfo.data && sessionInfo.data) {
                                    let saveRes = await salesforceService.createNewSession(sessionInfo.data, courseInfo.data);
                                    if (saveRes) {
                                        console.log("✅ New session created successfully.");
                                    } else {
                                        console.log("❌ Session creation failed.");
                                    }
                                }
                            } catch (error) {
                                console.error(`❌ Error creating session:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            console.log(`📋 WEBHOOK PAYLOAD:`, JSON.stringify(payload.payload, null, 2));

                            let courseInfo = await doceboService.getCourseInfo(payload.payload.course_id);
                            let sessionInfo = await doceboService.getCourseSessionInfo(payload.payload.session_id);

                            console.log(`📡 Session API Response Keys: ${sessionInfo?.data ? Object.keys(sessionInfo.data).join(', ') : 'No data'}`);
                            console.log(`📡 Session has instructor_id: ${sessionInfo?.data?.instructor_id ? 'YES' : 'NO'}`);
                            console.log(`📡 Session has instructors: ${sessionInfo?.data?.instructors ? 'YES' : 'NO'}`);

                            if (courseInfo.data && sessionInfo.data) {
                                let saveRes = await salesforceService.createNewSession(sessionInfo.data, courseInfo.data);
                                if (saveRes) {
                                    console.log("✅ New session created successfully.");
                                } else {
                                    console.log("❌ Session creation failed.");
                                }
                            }
                        } catch (error) {
                            console.error(`❌ Error creating session:`, error);
                        }
                    }
                    break;
                case 'ilt.session.updated':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let updateCourseInfo = await doceboService.getCourseInfo(payload.payloads[i].course_id);
                                let updateSessionInfo = await doceboService.getCourseSessionInfo(payload.payloads[i].session_id);
                                if (updateCourseInfo.data && updateSessionInfo.data) {
                                    let saveRes = await salesforceService.updateILTSession(updateSessionInfo.data, updateCourseInfo.data);
                                    if (saveRes) {
                                        console.log("Session updated successfully.");
                                    } else {
                                        console.log("Session update failed.");
                                    }
                                }
                            } catch (error) {
                                console.error(`Error updating session:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let updateCourseInfo = await doceboService.getCourseInfo(payload.payload.course_id);
                            let updateSessionInfo = await doceboService.getCourseSessionInfo(payload.payload.session_id);
                            if (updateCourseInfo.data && updateSessionInfo.data) {
                                let saveRes = await salesforceService.updateILTSession(updateSessionInfo.data, updateCourseInfo.data);
                                if (saveRes) {
                                    console.log("Session updated successfully.");
                                } else {
                                    console.log("Session update failed.");
                                }
                            }
                        } catch (error) {
                            console.error(`Error updating session:`, error);
                        }
                    }
                    break;
                case 'ilt.session.enrollment.created':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let createRes = await salesforceService.createIltSessionEnrollment(payload.payloads[i]);
                                if (createRes) {
                                    console.log("Session enrollment created successfully.");
                                } else {
                                    console.log("Session enrollment creation failed.");
                                }
                            } catch (error) {
                                console.error(`Error creating session enrollment:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let createRes = await salesforceService.createIltSessionEnrollment(payload.payload);
                            if (createRes) {
                                console.log("Session enrollment created successfully.");
                            } else {
                                console.log("Session enrollment creation failed.");
                            }
                        } catch (error) {
                            console.error(`Error creating session enrollment:`, error);
                        }
                    }
                    break;
                case 'ilt.session.enrollment.deleted':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let deleteRes = await salesforceService.deleteIltSessionEnrollment(payload.payloads[i]);
                                if (deleteRes) {
                                    console.log("Session enrollment deleted successfully.");
                                } else {
                                    console.log("Session enrollment delete failed.");
                                }
                            } catch (error) {
                                console.error(`Error deleting session enrollment:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let deleteRes = await salesforceService.deleteIltSessionEnrollment(payload.payload);
                            if (deleteRes) {
                                console.log("Session enrollment deleted successfully.");
                            } else {
                                console.log("Session enrollment delete failed.");
                            }
                        } catch (error) {
                            console.error(`Error deleting session enrollment:`, error);
                        }
                    }
                    break;
                default:
                    console.log(`Unknown session event: ${payload.event}`);
                    break;
            }
        } catch (error) {
            console.error("Error processing session task:", error);
        }
    } catch (error) {
        console.error("Error in session queue processing:", error);
    } finally {
        isProcessingSessionQueue = false;
        
        // If there are more items in the queue, process them after a small delay
        if (sessionManagementQueue.length > 0) {
            setTimeout(processSessionManagementQueue, 100);
        } else {
            console.log("Session management queue processing completed");
        }
    }
}

exports.sessionManagement = async (req, res) => {
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Webhook received, processing in background" });
    
    const payload = req.body;
    console.log('================= Session management ===============');
    console.log(payload);
    
    // Process the request asynchronously
    if (!messageQue.includes(payload.message_id)) {
        messageQue.push(payload.message_id);
        
        // Add to processing queue
        sessionManagementQueue.push({
            payload: payload
        });
        
        // Start processing if it's not already running
        if (sessionManagementQueue.length === 1) {
            processSessionManagementQueue();
        }
    }
}
// Queue to store learning plan management tasks for processing after response is sent
const lpManagementQueue = [];

// Flag to prevent multiple queue processing at the same time
let isProcessingLpQueue = false;

// Function to process the learning plan management queue in the background
async function processLpManagementQueue() {
    if (lpManagementQueue.length === 0 || isProcessingLpQueue) return;
    
    isProcessingLpQueue = true;
    console.log(`Processing ${lpManagementQueue.length} queued learning plan management tasks`);
    
    try {
        // Process one item at a time due to complexity of operations
        const task = lpManagementQueue.shift();
        const payload = task.payload;
        
        try {
            switch (payload.event) {
                case 'learningplan.enrollment.created':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let saveRes = await salesforceService.learningPlanEnrollment(payload.payloads[i]);
                                if (saveRes) {
                                    console.log("Learning plan enrollment created successfully.");
                                } else {
                                    console.log("Learning plan enrollment creation failed.");
                                }
                            } catch (error) {
                                console.error(`Error creating learning plan enrollment:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let saveRes = await salesforceService.learningPlanEnrollment(payload.payload);
                            if (saveRes) {
                                console.log("Learning plan enrollment created successfully.");
                            } else {
                                console.log("Learning plan enrollment creation failed.");
                            }
                        } catch (error) {
                            console.error(`Error creating learning plan enrollment:`, error);
                        }
                    }
                    break;
                case 'learningplan.enrollment.deleted':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let delRes = await salesforceService.learningPlanUnEnrollment(payload.payloads[i]);
                                if (delRes) {
                                    console.log("Learning plan enrollment deleted successfully.");
                                } else {
                                    console.log("Learning plan enrollment delete failed.");
                                }
                            } catch (error) {
                                console.error(`Error deleting learning plan enrollment:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let delRes = await salesforceService.learningPlanUnEnrollment(payload.payload);
                            if (delRes) {
                                console.log("Learning plan enrollment deleted successfully.");
                            } else {
                                console.log("Learning plan enrollment delete failed.");
                            }
                        } catch (error) {
                            console.error(`Error deleting learning plan enrollment:`, error);
                        }
                    }
                    break;
                case 'badge.earned':
                    // Logic for badge earned event
                    console.log("Badge earned event processing - not implemented");
                    break;
                case 'learningplan.enrollment.completed':
                    if (payload.payloads) {
                        for (let i = 0; i < payload.payloads.length; i++) {
                            try {
                                let updateRes = await salesforceService.learningPlanEnrollmentCompleted(payload.payloads[i]);
                                if (updateRes) {
                                    console.log("Learning plan enrollment completed successfully.");
                                } else {
                                    console.log("Learning plan enrollment completion failed.");
                                }
                            } catch (error) {
                                console.error(`Error completing learning plan enrollment:`, error);
                            }
                        }
                    } else if (payload.payload) {
                        try {
                            let updateRes = await salesforceService.learningPlanEnrollmentCompleted(payload.payload);
                            if (updateRes) {
                                console.log("Learning plan enrollment completed successfully.");
                            } else {
                                console.log("Learning plan enrollment completion failed.");
                            }
                        } catch (error) {
                            console.error(`Error completing learning plan enrollment:`, error);
                        }
                    }
                    break;
                default:
                    console.log(`Unknown learning plan event: ${payload.event}`);
                    break;
            }
        } catch (error) {
            console.error("Error processing learning plan task:", error);
        }
    } catch (error) {
        console.error("Error in learning plan queue processing:", error);
    } finally {
        isProcessingLpQueue = false;
        
        // If there are more items in the queue, process them after a small delay
        if (lpManagementQueue.length > 0) {
            setTimeout(processLpManagementQueue, 100);
        } else {
            console.log("Learning plan management queue processing completed");
        }
    }
}

exports.lpManagement = async (req, res) => {
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Webhook received, processing in background" });
    
    const payload = req.body;
    console.log('================= Learning plan management ===============');
    console.log(payload);
    
    // Process the request asynchronously
    if (!messageQue.includes(payload.message_id)) {
        messageQue.push(payload.message_id);
        
        // Add to processing queue
        lpManagementQueue.push({
            payload: payload
        });
        
        // Start processing if it's not already running
        if (lpManagementQueue.length === 1) {
            processLpManagementQueue();
        }
    }
}
// Queue to store course enrollment tasks for processing after response is sent
const courseEnrollmentQueue = [];

// Flag to prevent multiple queue processing at the same time
let isProcessingQueue = false;

// Function to process the enrollment queue in the background using batch processing
async function processCourseEnrollmentQueue() {
    if (courseEnrollmentQueue.length === 0 || isProcessingQueue) return;
    
    isProcessingQueue = true;
    console.log(`Processing ${courseEnrollmentQueue.length} queued course enrollments`);
    
    try {
        // Process multiple items at once (batch processing)
        const batchSize = 10; // Process up to 10 items at once
        const tasks = courseEnrollmentQueue.splice(0, batchSize);
        
        // Prepare all enrollments from tasks for batch processing
        const allEnrollments = [];
        
        // First gather all enrollment data
        for (const task of tasks) {
            if (task.type === 'multiple' && task.payloads && task.payloads.length > 0) {
                for (const payload of task.payloads) {
                    try {
                        // Check if user exists in Salesforce
                        const getSalesForceUserId = require("../salesforce/common/getSalesForceUserId");
                        const userSfId = await getSalesForceUserId(payload.user_id);
                        
                        // If user doesn't exist, create them
                        if (!userSfId) {
                            console.log(`User ${payload.user_id} not found in Salesforce. Creating user...`);
                            const userInfo = await doceboService.getUserInfo(payload.user_id);
                            if (userInfo.status == 200) {
                                let userData = userInfo.data;
                                // FIX: Don't set fired_at and expiration_date to empty strings
                                // Use current timestamp as creation date if not available
                                userData["fired_at"] = payload.enrollment_date || payload.created_at || new Date().toISOString().replace('T', ' ').slice(0, 19);
                                userData["expiration_date"] = userData["fired_at"]; // Use same date as creation date
                                let userListedInfo = await doceboService.getUserListedInfo(payload.user_id);

                                // Enhanced logging for course enrollment user creation
                                console.log(`📥 COURSE ENROLLMENT USER CREATION DATA for User ${payload.user_id}:`);
                                console.log("Raw userData from Docebo API:", JSON.stringify(userData, null, 2));
                                console.log("Raw userListedInfo:", JSON.stringify(userListedInfo, null, 2));
                                console.log("Course enrollment payload data:", JSON.stringify(payload, null, 2));

                                const { createNewUser } = require("../salesforce/users/createUser");
                                const userSaveRes = await createNewUser(userData, userListedInfo);
                                if (!userSaveRes) {
                                    console.error(`Failed to create user ${payload.user_id} in Salesforce`);
                                    continue;
                                }
                                console.log(`User ${payload.user_id} created successfully in Salesforce`);
                            } else {
                                console.error(`Could not get user info from Docebo for user ${payload.user_id}`);
                                continue;
                            }
                        }
                        
                        const tmpInfo = await doceboService.getEnrolledInfo(payload.course_id, payload.user_id);
                        const courseInfo = await doceboService.getCourseInfo(payload.course_id);
                        
                        if (tmpInfo && tmpInfo.data && tmpInfo.data.records) {
                            allEnrollments.push({
                                payload: payload,
                                record: tmpInfo.data.records,
                                courseInfo: courseInfo,
                                user_id: payload.user_id,
                                course_id: payload.course_id
                            });
                        }
                    } catch (error) {
                        console.error(`Error preparing enrollment data for user ${payload.user_id}:`, error);
                    }
                }
            } else if (task.type === 'single' && task.payload) {
                try {
                    // Check if user exists in Salesforce
                    const getSalesForceUserId = require("../salesforce/common/getSalesForceUserId");
                    const userSfId = await getSalesForceUserId(task.payload.user_id);
                    
                    // If user doesn't exist, create them
                    if (!userSfId) {
                        console.log(`User ${task.payload.user_id} not found in Salesforce. Creating user...`);
                        const userInfo = await doceboService.getUserInfo(task.payload.user_id);
                        if (userInfo.status == 200) {
                            let userData = userInfo.data;
                            // FIX: Don't set fired_at and expiration_date to empty strings
                            // Use current timestamp as creation date if not available
                            userData["fired_at"] = task.payload.enrollment_date || task.payload.created_at || new Date().toISOString().replace('T', ' ').slice(0, 19);
                            userData["expiration_date"] = userData["fired_at"]; // Use same date as creation date
                            let userListedInfo = await doceboService.getUserListedInfo(task.payload.user_id);
                            
                            const { createNewUser } = require("../salesforce/users/createUser");
                            const userSaveRes = await createNewUser(userData, userListedInfo);
                            if (!userSaveRes) {
                                console.error(`Failed to create user ${task.payload.user_id} in Salesforce`);
                                continue;
                            }
                            console.log(`User ${task.payload.user_id} created successfully in Salesforce`);
                        } else {
                            console.error(`Could not get user info from Docebo for user ${task.payload.user_id}`);
                            continue;
                        }
                    }
                    
                    const tmpInfo = await doceboService.getEnrolledInfo(task.payload.course_id, task.payload.user_id);
                    const courseInfo = await doceboService.getCourseInfo(task.payload.course_id);
                    
                    if (tmpInfo && tmpInfo.data && tmpInfo.data.records) {
                        allEnrollments.push({
                            payload: task.payload,
                            record: tmpInfo.data.records,
                            courseInfo: courseInfo,
                            user_id: task.payload.user_id,
                            course_id: task.payload.course_id
                        });
                    }
                } catch (error) {
                    console.error(`Error preparing enrollment data for user ${task.payload.user_id}:`, error);
                }
            }
        }
        
        // Process all enrollments
        if (allEnrollments.length > 0) {
            console.log(`Batch processing ${allEnrollments.length} enrollments`);
            
            try {
                // Try to use batch processing if available
                // Import the batch processing function dynamically to avoid circular references
                const { saveCourseEnrollmentsInBatch } = require("../salesforce/courseEnrollment/createCourseEnrollment");
                
                if (typeof saveCourseEnrollmentsInBatch === 'function') {
                    // Format the data for batch processing
                    const batchData = allEnrollments.map(item => ({
                        course_id: item.course_id,
                        user_id: item.user_id,
                        enrollment_date: item.payload.enrollment_date || item.payload.created_at || new Date().toISOString(),
                        completion_date: item.payload.completion_date || null,
                        status: item.payload.status || "A", // FIX: Use valid picklist value "A" instead of "Enrolled"
                        score: item.payload.score || 0,
                        total_time: item.payload.total_time || item.payload.time_in_course || 0, // Add time data for Time_in_course__c field
                        time_in_course: item.payload.total_time || item.payload.time_in_course || 0, // Alternative field name
                        completed_learning_objects: item.payload.completed_learning_objects || 0,
                        completion: item.payload.completion || item.payload.completion_percentage || 0,
                        credits: item.payload.credits || 0,
                        unenrollment_date: item.payload.unenrollment_date || null
                    }));
                    
                    // Process in batch
                    const batchResult = await saveCourseEnrollmentsInBatch(batchData);
                    console.log(`Batch enrollment processing result: ${batchResult ? 'Success' : 'Failed'}`);
                } else {
                    // Fallback to individual processing if batch function not available
                    console.log("Batch function not available, processing items individually");
                    for (const item of allEnrollments) {
                        const createCourseEnrollmentRest = await salesforceService.updateCourseEnrollment({
                            payload: item.payload,
                            record: item.record
                        }, item.courseInfo);
                        
                        if (createCourseEnrollmentRest) {
                            console.log(`New course enrollment saved correctly for user ${item.user_id}.`);
                        } else {
                            console.log(`New course enrollment saving failed for user ${item.user_id}.`);
                        }
                    }
                }
            } catch (error) {
                console.error("Error during batch course enrollment processing:", error);
                
                // Fallback to individual processing if batch processing fails
                console.log("Falling back to individual processing after batch error");
                for (const item of allEnrollments) {
                    try {
                        const createCourseEnrollmentRest = await salesforceService.updateCourseEnrollment({
                            payload: item.payload,
                            record: item.record
                        }, item.courseInfo);
                        
                        if (createCourseEnrollmentRest) {
                            console.log(`New course enrollment saved correctly for user ${item.user_id}.`);
                        } else {
                            console.log(`New course enrollment saving failed for user ${item.user_id}.`);
                        }
                    } catch (individualError) {
                        console.error(`Error processing individual enrollment for user ${item.user_id}:`, individualError);
                    }
                }
            }
        }
    } catch (error) {
        console.error("Error in queue processing:", error);
    } finally {
        isProcessingQueue = false;
        
        // If there are more items in the queue, process them after a small delay
        if (courseEnrollmentQueue.length > 0) {
            setTimeout(processCourseEnrollmentQueue, 100);
        } else {
            console.log("Course enrollment queue processing completed");
        }
    }
}

exports.courseEnrollmentCreated = async (req, res) => {
    let payload = req.body;
    console.log('================= Course Enroll Created. ===============');
    
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Webhook received, processing in background" });
    
    // Process the request asynchronously
    if (!messageQue.includes(req.body.message_id)) {
        messageQue.push(req.body.message_id);
        
        if (payload.payloads && payload.payloads.length > 0) {
            // Add to processing queue
            courseEnrollmentQueue.push({
                type: 'multiple',
                payloads: payload.payloads
            });
        }
        
        if (payload.payload) {
            // Add to processing queue
            courseEnrollmentQueue.push({
                type: 'single',
                payload: payload.payload
            });
        }
        
        // Start processing if it's not already running
        if (courseEnrollmentQueue.length === 1) {
            processCourseEnrollmentQueue();
        }
    }
}
exports.courseUnEnrollment = async (req, res) => {
    let payloads = req.body.payloads;
    if (!messageQue.includes(req.body.message_id)) {
        messageQue.push(req.body.message_id);
        if (payloads == undefined) {
            if (req.body.payload) {
                payloads = [];
                payloads[0] = req.body.payload;
            }
        }
        if (Array.isArray(payloads)) {
            if (payloads.length > 0) {
                for (let i = 0; i < payloads.length; i++) {
                    let deleteCourseEnrollmentRest = await salesforceService.deleteCourseEnrollment(payloads[i].course_id, payloads[i].user_id);
                    if (deleteCourseEnrollmentRest == "success") {
                        console.log("Course Enrolment deleted correctly.");
                    } else {
                        console.log(deleteCourseEnrollmentRest);
                    }
                }
            }
        }
    }
    res.status(200).send({ status: "success", message: "Course Enrolment deleted!" });
}
// Queue to store course completion tasks for processing after response is sent
const courseCompletionQueue = [];

// Flag to prevent multiple queue processing at the same time
let isProcessingCompletionQueue = false;

// Function to process the course completion queue in the background
async function processCourseCompletionQueue() {
    if (courseCompletionQueue.length === 0 || isProcessingCompletionQueue) return;
    
    isProcessingCompletionQueue = true;
    console.log(`Processing ${courseCompletionQueue.length} queued course completions`);
    
    try {
        // Process multiple items at once (batch processing)
        const batchSize = 10; // Process up to 10 items at once
        const tasks = courseCompletionQueue.splice(0, batchSize);
        
        for (const task of tasks) {
            try {
                let payloads = task.payload;
                if (payloads == undefined && task.req_body && task.req_body.payload) {
                    payloads = [];
                    payloads[0] = task.req_body.payload;
                }
                
                let enrollmentInfo = [];
                if (payloads !== undefined) {
                    if (Array.isArray(payloads)) {
                        for (let i = 0; i < payloads.length; i++) {
                            try {
                                let tmpInfo = await doceboService.getEnrolledInfo(payloads[i].course_id, payloads[i].user_id);
                                if (tmpInfo && tmpInfo.data && tmpInfo.data.records) {
                                    enrollmentInfo.push({ payload: payloads[i], record: tmpInfo.data.records });
                                }
                            } catch (error) {
                                console.error(`Error getting enrolled info for course ${payloads[i].course_id}, user ${payloads[i].user_id}:`, error);
                            }
                        }
                    } else if (typeof payloads === 'object' && payloads !== null) {
                        try {
                            let tmpInfo = await doceboService.getEnrolledInfo(payloads.course_id, payloads.user_id);
                            if (tmpInfo && tmpInfo.data && tmpInfo.data.records) {
                                enrollmentInfo.push({ payload: payloads, record: tmpInfo.data.records });
                            }
                        } catch (error) {
                            console.error(`Error getting enrolled info for course ${payloads.course_id}, user ${payloads.user_id}:`, error);
                        }
                    }
                    
                    if (enrollmentInfo.length > 0) {
                        let updateCourseEnrollmentRes = await salesforceService.updateCourseEnrollment(enrollmentInfo);
                        if (updateCourseEnrollmentRes) {
                            console.log("Course completion processed successfully.");
                        } else {
                            console.error("Course completion processing failed.");
                        }
                    } else {
                        console.log("No valid enrollment info to process for course completion.");
                    }
                }
            } catch (error) {
                console.error("Error processing course completion task:", error);
            }
        }
    } catch (error) {
        console.error("Error in course completion queue processing:", error);
    } finally {
        isProcessingCompletionQueue = false;
        
        // If there are more items in the queue, process them after a small delay
        if (courseCompletionQueue.length > 0) {
            setTimeout(processCourseCompletionQueue, 100);
        } else {
            console.log("Course completion queue processing completed");
        }
    }
}

exports.courseCompleted = async (req, res) => {
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Webhook received, processing in background" });
    
    // Process the request asynchronously
    if (!messageQue.includes(req.body.message_id)) {
        messageQue.push(req.body.message_id);
        console.log('================= Course Completed. ===============');
        console.log(req.body.payload);
        
        // Add to processing queue
        courseCompletionQueue.push({
            payload: req.body.payload,
            req_body: req.body
        });
        
        // Start processing if it's not already running
        if (courseCompletionQueue.length === 1) {
            processCourseCompletionQueue();
        }
    }
}

// New endpoint to get all course enrollments for a user
exports.getUserEnrollments = async (req, res) => {
    try {
        const userId = req.params.userId;
        const includeDetails = req.query.details === 'true';
        const format = req.query.format || 'summary'; // summary, basic, detailed

        console.log(`📚 API Request: Getting enrollments for User ID: ${userId}`);
        console.log(`   Format: ${format}, Include Details: ${includeDetails}`);

        if (!userId) {
            return res.status(400).json({
                success: false,
                error: "User ID is required",
                message: "Please provide a valid user ID in the URL path"
            });
        }

        let result;

        switch (format) {
            case 'basic':
                console.log('📋 Fetching basic enrollments...');
                const basicEnrollments = await getUserCourseEnrollments(userId);
                result = {
                    success: true,
                    user_id: userId,
                    format: 'basic',
                    total_enrollments: basicEnrollments.length,
                    enrollments: basicEnrollments
                };
                break;

            case 'detailed':
                console.log('📋 Fetching detailed enrollments...');
                const detailedEnrollments = await getUserEnrollmentsWithCourseDetails(userId);
                result = {
                    success: true,
                    user_id: userId,
                    format: 'detailed',
                    total_enrollments: detailedEnrollments.length,
                    enrollments: detailedEnrollments
                };
                break;

            case 'summary':
            default:
                console.log('📊 Fetching enrollment summary...');
                const summary = await getUserEnrollmentsSummary(userId);
                result = {
                    success: true,
                    user_id: userId,
                    format: 'summary',
                    ...summary
                };
                break;
        }

        console.log(`✅ Successfully retrieved ${result.total_enrollments || 0} enrollments for user ${userId}`);

        res.status(200).json(result);

    } catch (error) {
        console.error(`💥 Error getting enrollments for user ${req.params.userId}:`, error);

        res.status(500).json({
            success: false,
            error: error.message,
            message: "Failed to retrieve user enrollments"
        });
    }
}

// Queue to store instructor assignment tasks for processing after response is sent
const instructorAssignmentQueue = [];

// Flag to prevent multiple queue processing at the same time
let isProcessingInstructorQueue = false;

// Function to process the instructor assignment queue in the background
async function processInstructorAssignmentQueue() {
    if (instructorAssignmentQueue.length === 0 || isProcessingInstructorQueue) return;

    isProcessingInstructorQueue = true;
    console.log(`Processing ${instructorAssignmentQueue.length} queued instructor assignments`);

    try {
        // Process multiple items at once (batch processing)
        const batchSize = 5; // Process up to 5 items at once for instructor assignments
        const tasks = instructorAssignmentQueue.splice(0, batchSize);

        for (const task of tasks) {
            try {
                const payload = task.payload;
                console.log(`Processing instructor assignment: User ${payload.instructor_user_id} to Session ${payload.session_id}`);

                // Get instructor data from Docebo API
                const instructorResponse = await doceboService.getInstructorData(
                    payload.instructor_user_id,
                    payload.course_id,
                    payload.session_id
                );

                if (!instructorResponse || instructorResponse.status !== 200 || !instructorResponse.data) {
                    console.error(`Failed to retrieve instructor data for user ${payload.instructor_user_id}`);
                    continue;
                }

                console.log(`Instructor data retrieved successfully for user ${payload.instructor_user_id}`);

                // Import the createInstructor function
                const { createInstructor } = require("../salesforce/instructors/createInstructor");

                // Create/update instructor in Salesforce
                const result = await createInstructor(
                    instructorResponse.data,
                    payload.instructor_user_id,
                    payload.course_id,
                    payload.session_id,
                    payload.instructor_details // Pass any instructor details from webhook
                );

                if (result) {
                    console.log(`✅ Instructor ${payload.instructor_user_id} successfully assigned to session ${payload.session_id}`);
                } else {
                    console.error(`❌ Failed to assign instructor ${payload.instructor_user_id} to session ${payload.session_id}`);
                }

            } catch (error) {
                console.error("Error processing instructor assignment task:", error);
            }
        }
    } catch (error) {
        console.error("Error in instructor assignment queue processing:", error);
    } finally {
        isProcessingInstructorQueue = false;

        // If there are more items in the queue, process them after a small delay
        if (instructorAssignmentQueue.length > 0) {
            setTimeout(processInstructorAssignmentQueue, 1000);
        } else {
            console.log("Instructor assignment queue processing completed");
        }
    }
}

// Course management queue for processing webhooks
let courseManagementQueue = [];
let isProcessingCourseQueue = false;

async function processCourseManagementQueue() {
    if (isProcessingCourseQueue || courseManagementQueue.length === 0) {
        return;
    }

    isProcessingCourseQueue = true;
    console.log(`🔄 Processing course management queue: ${courseManagementQueue.length} items`);

    try {
        while (courseManagementQueue.length > 0) {
            const task = courseManagementQueue.shift();

            try {
                const { createNewCourse } = require("../salesforce/courses/createCourse");

                console.log(`📚 Processing ${task.event} for course ${task.courseId}`);
                console.log(`📋 Course webhook payload:`, JSON.stringify(task.payload, null, 2));

                // Handle different course events
                switch (task.event) {
                    case 'course.created':
                    case 'course.updated':
                    case 'course.properties_changed':
                        // Get comprehensive course data from Docebo
                        const courseInfo = await doceboService.getCourseInfo(task.courseId);

                        if (courseInfo && courseInfo.status === 200) {
                            const courseData = courseInfo.data;

                            // Enhanced logging for webhook course data
                            console.log(`📥 WEBHOOK COURSE DATA for Course ${task.courseId}:`);
                            console.log(`   Course Name: "${courseData.name}"`);
                            console.log(`   Course Type: "${courseData.type}"`);
                            console.log(`   Course Status: "${courseData.status}"`);
                            console.log(`   Language: "${courseData.language ? courseData.language.name : 'N/A'}"`);
                            console.log(`   Category: "${courseData.category ? courseData.category.name : 'No category'}"`);
                            console.log(`   Category Code: "${courseData.category ? courseData.category.code : 'No code'}"`);
                            console.log(`   Skills: ${courseData.skills ? JSON.stringify(courseData.skills) : 'None'}`);
                            console.log(`   Duration: ${courseData.time_options ? courseData.time_options.duration.days : 'N/A'} days`);
                            console.log(`   Credits: ${courseData.credits || 0}`);
                            console.log(`   Thumbnail: ${courseData.thumbnail ? courseData.thumbnail.url : 'None'}`);
                            console.log(`   Slug: "${courseData.slug_name || 'N/A'}"`);

                            // Create or update course in Salesforce with complete data mapping
                            const result = await createNewCourse(courseData);

                            if (result) {
                                console.log(`✅ Course ${task.courseId} successfully processed via webhook`);
                                console.log(`   Salesforce Course ID: ${result}`);

                                // Log successful field mapping
                                console.log(`📊 WEBHOOK SUCCESS SUMMARY:`);
                                console.log(`   ✅ Course created/updated in Salesforce`);
                                console.log(`   ✅ Category data mapped: "${courseData.category ? courseData.category.name : 'Empty'}"`);
                                console.log(`   ✅ All course fields synchronized`);

                            } else {
                                console.error(`❌ Failed to process course ${task.courseId} via webhook`);
                            }
                        } else {
                            console.error(`❌ Failed to get course info for ${task.courseId}`);
                            console.error(`   API Response Status: ${courseInfo ? courseInfo.status : 'No response'}`);
                        }
                        break;

                    case 'course.deleted':
                        // Handle course deletion
                        console.log(`🗑️ Processing course deletion for course ${task.courseId}`);

                        try {
                            const getConnection = require("../salesforce/common/getConnection");
                            const conn = await getConnection();

                            if (conn && conn.accessToken) {
                                // Find the course in Salesforce
                                const existingCourse = await conn.sobject("Docebo_Course__c")
                                    .findOne({ Course_External_Id__c: parseInt(task.courseId) });

                                if (existingCourse) {
                                    // Mark course as deleted instead of actually deleting it
                                    const updateResult = await conn.sobject("Docebo_Course__c").update({
                                        Id: existingCourse.Id,
                                        Deleted__c: true,
                                        Deletion_Date__c: new Date().toISOString(),
                                        Effective__c: false // Mark as inactive
                                    });

                                    if (updateResult.success) {
                                        console.log(`✅ Course ${task.courseId} marked as deleted in Salesforce`);
                                        console.log(`   Salesforce Course ID: ${existingCourse.Id}`);
                                        console.log(`   Deletion Date: ${new Date().toISOString()}`);

                                        console.log(`📊 DELETION SUCCESS SUMMARY:`);
                                        console.log(`   ✅ Course marked as deleted in Salesforce`);
                                        console.log(`   ✅ Deletion date recorded`);
                                        console.log(`   ✅ Course marked as inactive`);

                                    } else {
                                        console.error(`❌ Failed to mark course ${task.courseId} as deleted:`, updateResult.errors);
                                    }
                                } else {
                                    console.log(`⚠️ Course ${task.courseId} not found in Salesforce for deletion`);
                                }
                            } else {
                                console.error(`❌ Invalid Salesforce connection for course deletion`);
                            }
                        } catch (deletionError) {
                            console.error(`❌ Error processing course deletion for ${task.courseId}:`, deletionError);
                        }
                        break;

                    default:
                        console.log(`⚠️ Unhandled course event: ${task.event}`);
                }

            } catch (taskError) {
                console.error(`❌ Error processing course ${task.courseId}:`, taskError);
            }

            // Small delay between processing items
            await new Promise(resolve => setTimeout(resolve, 500));
        }

    } catch (error) {
        console.error("❌ Error in course queue processing:", error);
    } finally {
        isProcessingCourseQueue = false;

        // If there are more items in the queue, process them after a small delay
        if (courseManagementQueue.length > 0) {
            setTimeout(processCourseManagementQueue, 1000);
        } else {
            console.log("✅ Course management queue processing completed");
        }
    }
}

exports.courseManagement = async (req, res) => {
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Course webhook received, processing in background" });

    const payload = req.body;
    console.log('================= Course Management Webhook ===============');
    console.log('📥 Raw webhook payload:', JSON.stringify(payload, null, 2));

    // Process the request asynchronously
    if (!messageQue.includes(payload.message_id)) {
        messageQue.push(payload.message_id);

        try {
            // Handle different payload structures
            if (payload.payloads && Array.isArray(payload.payloads)) {
                // Multiple courses in payload
                console.log(`📦 Processing ${payload.payloads.length} courses from webhook`);

                payload.payloads.forEach(coursePayload => {
                    courseManagementQueue.push({
                        event: payload.event,
                        courseId: coursePayload.course_id,
                        payload: coursePayload,
                        timestamp: new Date().toISOString()
                    });
                });

            } else if (payload.payload && payload.payload.course_id) {
                // Single course in payload
                console.log(`📦 Processing single course from webhook`);

                courseManagementQueue.push({
                    event: payload.event,
                    courseId: payload.payload.course_id,
                    payload: payload.payload,
                    timestamp: new Date().toISOString()
                });

            } else {
                console.error('❌ Invalid webhook payload structure - no course_id found');
                return;
            }

            // Start processing if it's not already running
            if (courseManagementQueue.length === 1) {
                processCourseManagementQueue();
            }

        } catch (error) {
            console.error('❌ Error processing course webhook:', error);
        }
    } else {
        console.log('⚠️ Duplicate webhook message ignored');
    }
};

exports.sessionInstructorAssigned = async (req, res) => {
    // Immediately respond to prevent timeout
    res.status(200).send({ status: "success", message: "Instructor assignment webhook received, processing in background" });

    const payload = req.body;
    console.log('================= Session Instructor Assigned ===============');
    console.log('Webhook payload:', JSON.stringify(payload, null, 2));

    // Process the request asynchronously
    if (!messageQue.includes(payload.message_id)) {
        messageQue.push(payload.message_id);

        // Handle both single payload and multiple payloads
        if (payload.payloads && payload.payloads.length > 0) {
            // Multiple instructor assignments
            payload.payloads.forEach(singlePayload => {
                instructorAssignmentQueue.push({
                    payload: singlePayload
                });
            });
        } else if (payload.payload) {
            // Single instructor assignment
            instructorAssignmentQueue.push({
                payload: payload.payload
            });
        }

        // Start processing if it's not already running
        if (instructorAssignmentQueue.length === 1) {
            processInstructorAssignmentQueue();
        }
    }
}

