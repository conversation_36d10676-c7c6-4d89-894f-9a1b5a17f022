require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testEnrollmentFieldMapping() {
    try {
        console.log('🔍 Testing Course Enrollment Field Mapping');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check the actual field names in Salesforce
        console.log('\n📋 CHECKING SALESFORCE FIELD NAMES...');
        console.log('-'.repeat(50));
        
        try {
            const enrollmentObjectDesc = await conn.sobject("Docebo_CourseEnrollment__c").describe();
            
            console.log(`✅ Found ${enrollmentObjectDesc.fields.length} fields in Docebo_CourseEnrollment__c object:`);
            
            // Look for the specific fields we're interested in
            const fieldsOfInterest = [
                'enrollment_id__c',
                'Enrollment_ID__c', 
                'EnrolmentId__c',
                'time_in_course__c',
                'Time_In_Course__c',
                'Time_in_Course__c'
            ];
            
            const foundFields = [];
            const missingFields = [];
            
            fieldsOfInterest.forEach(fieldName => {
                const field = enrollmentObjectDesc.fields.find(f => 
                    f.name.toLowerCase() === fieldName.toLowerCase()
                );
                
                if (field) {
                    foundFields.push({
                        searchName: fieldName,
                        actualName: field.name,
                        type: field.type,
                        required: !field.nillable,
                        externalId: field.externalId
                    });
                } else {
                    missingFields.push(fieldName);
                }
            });
            
            console.log('\n✅ FOUND FIELDS:');
            foundFields.forEach(field => {
                console.log(`   • ${field.actualName} (searched: ${field.searchName})`);
                console.log(`     Type: ${field.type}, Required: ${field.required}, External ID: ${field.externalId}`);
            });
            
            console.log('\n❌ MISSING FIELDS:');
            missingFields.forEach(field => {
                console.log(`   • ${field}`);
            });
            
            // Find the correct external ID field
            const externalIdFields = enrollmentObjectDesc.fields.filter(f => f.externalId);
            console.log('\n🔑 EXTERNAL ID FIELDS:');
            externalIdFields.forEach(field => {
                console.log(`   • ${field.name} (${field.type})`);
            });
            
        } catch (describeError) {
            console.error('❌ Error describing Docebo_CourseEnrollment__c object:', describeError);
        }

        // Step 2: Test enrollment creation with correct field names
        console.log('\n🧪 TESTING ENROLLMENT CREATION...');
        console.log('-'.repeat(50));
        
        // Get a sample user and course for testing
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({}, ['Id', 'User_External_Id__c', 'FirstName', 'LastName']);
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({}, ['Id', 'Course_External_Id__c', 'Course_Name__c']);
        
        if (!sampleUser || !sampleCourse) {
            console.log('⚠️ No sample user or course found for testing');
            return;
        }
        
        console.log(`📚 Using sample course: ${sampleCourse.Course_Name__c} (ID: ${sampleCourse.Course_External_Id__c})`);
        console.log(`👤 Using sample user: ${sampleUser.FirstName} ${sampleUser.LastName} (ID: ${sampleUser.User_External_Id__c})`);
        
        // Create test enrollment data
        const testEnrollmentData = {
            Course__c: sampleCourse.Id,
            Docebo_User__c: sampleUser.Id,
            Completed_Learning_Objects__c: 5,
            Completion__c: 75,
            Completion_Date__c: "",
            Credits__c: 3,
            Enrollment_Date__c: new Date().toISOString(),
            Score__c: 85,
            Status__c: "A",
            Unenrollment_Date__c: "",
            Time_In_Course__c: 3600, // 1 hour in seconds
            Enrollment_ID__c: `TEST-${sampleCourse.Course_External_Id__c}-${sampleUser.User_External_Id__c}-${Date.now()}`
        };
        
        console.log('\n📝 Test enrollment data:');
        Object.entries(testEnrollmentData).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
        
        try {
            // Try to create the enrollment
            const result = await conn.sobject("Docebo_CourseEnrollment__c")
                .create(testEnrollmentData);
            
            if (result.success) {
                console.log(`✅ Test enrollment created successfully: ${result.id}`);
                
                // Verify the created enrollment
                const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                    .findOne({ Id: result.id });
                
                console.log('\n📊 CREATED ENROLLMENT VERIFICATION:');
                console.log(`   Enrollment ID: ${createdEnrollment.Id}`);
                console.log(`   External ID: ${createdEnrollment.Enrollment_ID__c}`);
                console.log(`   Time in Course: ${createdEnrollment.Time_In_Course__c} seconds`);
                console.log(`   Completion: ${createdEnrollment.Completion__c}%`);
                console.log(`   Score: ${createdEnrollment.Score__c}`);
                console.log(`   Status: ${createdEnrollment.Status__c}`);
                
                // Check if both fields are populated correctly
                const enrollmentIdWorking = createdEnrollment.Enrollment_ID__c === testEnrollmentData.Enrollment_ID__c;
                const timeInCourseWorking = createdEnrollment.Time_In_Course__c === testEnrollmentData.Time_In_Course__c;
                
                console.log('\n🎯 FIELD MAPPING RESULTS:');
                console.log(`   Enrollment_ID__c: ${enrollmentIdWorking ? '✅' : '❌'} ${enrollmentIdWorking ? 'WORKING' : 'NOT WORKING'}`);
                console.log(`   Time_In_Course__c: ${timeInCourseWorking ? '✅' : '❌'} ${timeInCourseWorking ? 'WORKING' : 'NOT WORKING'}`);
                
                // Clean up test data
                await conn.sobject("Docebo_CourseEnrollment__c").delete(result.id);
                console.log('🗑️ Test enrollment cleaned up');
                
            } else {
                console.error('❌ Failed to create test enrollment:', result.errors);
            }
            
        } catch (createError) {
            console.error('❌ Error creating test enrollment:', createError);
            
            // Check if it's a field name issue
            if (createError.message.includes('INVALID_FIELD')) {
                console.log('🔍 This appears to be a field name issue. Checking field names...');
                
                // Try with different field name variations
                const variations = [
                    { Enrollment_ID__c: testEnrollmentData.Enrollment_ID__c },
                    { enrollment_id__c: testEnrollmentData.Enrollment_ID__c },
                    { EnrolmentId__c: testEnrollmentData.Enrollment_ID__c }
                ];
                
                for (const variation of variations) {
                    try {
                        const testData = { ...testEnrollmentData, ...variation };
                        delete testData.Enrollment_ID__c; // Remove the original field
                        
                        console.log(`🧪 Testing with field: ${Object.keys(variation)[0]}`);
                        
                        const testResult = await conn.sobject("Docebo_CourseEnrollment__c")
                            .create(testData);
                        
                        if (testResult.success) {
                            console.log(`✅ SUCCESS with field: ${Object.keys(variation)[0]}`);
                            await conn.sobject("Docebo_CourseEnrollment__c").delete(testResult.id);
                            break;
                        }
                        
                    } catch (variationError) {
                        console.log(`❌ Failed with field: ${Object.keys(variation)[0]}`);
                    }
                }
            }
        }

        // Step 3: Summary and recommendations
        console.log('\n📊 ENROLLMENT FIELD MAPPING SUMMARY:');
        console.log('=' .repeat(60));
        
        console.log('🔍 FIELD NAME INVESTIGATION:');
        console.log('   • Check Salesforce object field names');
        console.log('   • Verify external ID field configuration');
        console.log('   • Test field mapping with actual data');
        
        console.log('\n🔧 RECOMMENDED FIXES:');
        console.log('   • Update field names to match Salesforce exactly');
        console.log('   • Ensure Time_In_Course__c is mapped from enrollment data');
        console.log('   • Verify external ID field for upsert operations');
        
        return {
            success: true,
            message: 'Enrollment field mapping test completed'
        };

    } catch (error) {
        console.error('💥 Error in enrollment field mapping test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting enrollment field mapping test...');
testEnrollmentFieldMapping()
    .then((result) => {
        console.log('\n✅ Enrollment field mapping test completed');
        if (result.success) {
            console.log('🎉 Test completed successfully!');
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
